/**
 * Claude Code - 主入口文件
 * 
 * 这是Claude Code CLI工具的主入口点，负责初始化应用程序并启动CLI界面。
 * 
 * @original: 原始文件中的ER8()函数和相关初始化代码
 */

import { createCLI } from './cli/index.js';
import { initializeGlobalState } from './core/session.js';
import { setupEnvironment } from './utils/environment.js';
import { logger } from './utils/logger.js';

/**
 * 主应用程序入口点
 * 初始化全局状态、设置环境并启动CLI
 * 
 * @original: KR8() 和相关初始化函数
 */
async function main() {
  try {
    // 设置环境和全局状态
    setupEnvironment();
    initializeGlobalState();
    
    // 创建并启动CLI应用
    const cli = await createCLI();
    await cli.parseAsync(process.argv);
    
  } catch (error) {
    logger.error('应用程序启动失败:', error);
    process.exit(1);
  }
}

/**
 * 导出主要功能供外部使用
 * @original: export语句在文件末尾
 */
export { 
  main as default,
  createCLI,
  initializeGlobalState,
  setupEnvironment
};

// 如果直接运行此文件，则启动主程序
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(error => {
    console.error('未捕获的错误:', error);
    process.exit(1);
  });
}
