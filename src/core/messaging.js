/**
 * 消息处理系统
 * 
 * 管理用户消息、助手响应、工具调用和消息流处理
 * 
 * @original: 消息处理相关代码 (L22995-23494, L29132-29381, L45137-45991)
 */

import { logger } from '../utils/logger.js';
import { logTelemetryEvent } from '../services/telemetry.js';
import { getCurrentModel } from './models.js';

/**
 * 消息类型枚举
 */
export const MESSAGE_TYPES = {
  USER: 'user',
  ASSISTANT: 'assistant',
  SYSTEM: 'system',
  TOOL_USE: 'tool_use',
  TOOL_RESULT: 'tool_result',
  STREAM_REQUEST_START: 'stream_request_start',
  STREAM_REQUEST_END: 'stream_request_end'
};

/**
 * 消息状态枚举
 */
export const MESSAGE_STATUS = {
  PENDING: 'pending',
  PROCESSING: 'processing',
  COMPLETED: 'completed',
  FAILED: 'failed',
  CANCELLED: 'cancelled'
};

/**
 * 工具使用状态枚举
 */
export const TOOL_USE_STATUS = {
  PENDING: 'pending',
  IN_PROGRESS: 'in_progress',
  COMPLETED: 'completed',
  FAILED: 'failed'
};

/**
 * 消息处理器类
 */
export class MessageProcessor {
  constructor() {
    this.messageQueue = [];
    this.processingQueue = new Set();
    this.inProgressToolUseIDs = new Set();
    this.messageHandlers = new Map();
    this.streamHandlers = new Map();
  }

  /**
   * 处理消息流
   * @original: RRB()函数 (L22995-23300)
   * @param {string} sessionId - 会话ID
   * @param {Array} messages - 消息数组
   * @param {Object} options - 处理选项
   * @param {Object} context - 处理上下文
   * @returns {AsyncGenerator} 消息流生成器
   */
  async *processMessageStream(sessionId, messages, options, context) {
    try {
      yield {
        type: MESSAGE_TYPES.STREAM_REQUEST_START,
        sessionId,
        timestamp: new Date().toISOString()
      };

      // 验证消息格式
      this.validateMessages(messages);

      // 处理系统提示
      const systemPrompts = await this.processSystemPrompts(options.systemPrompt || []);

      // 构建API请求
      const apiRequest = await this.buildAPIRequest(messages, systemPrompts, options);

      // 发送请求并处理响应流
      yield* this.handleAPIResponseStream(apiRequest, context);

      yield {
        type: MESSAGE_TYPES.STREAM_REQUEST_END,
        sessionId,
        timestamp: new Date().toISOString()
      };

    } catch (error) {
      logger.error('处理消息流失败', { sessionId, error: error.message });
      
      yield {
        type: 'error',
        error: error.message,
        timestamp: new Date().toISOString()
      };
    }
  }

  /**
   * 处理工具使用流
   * @original: wq8()函数 (L45306-45311)
   * @param {Array} toolUses - 工具使用数组
   * @param {Array} messages - 消息数组
   * @param {Object} options - 处理选项
   * @param {Object} context - 处理上下文
   * @returns {AsyncGenerator} 工具使用流生成器
   */
  async *processToolUseStream(toolUses, messages, options, context) {
    try {
      for (const toolUse of toolUses) {
        const correspondingMessage = messages.find(msg => 
          msg.content.some(content => 
            content.type === 'tool_use' && content.id === toolUse.id
          )
        );

        yield* this.executeToolUse(toolUse, correspondingMessage, options, context);
      }

    } catch (error) {
      logger.error('处理工具使用流失败', { error: error.message });
      throw error;
    }
  }

  /**
   * 执行单个工具使用
   * @original: XuB()函数 (L45315-45366)
   * @param {Object} toolUse - 工具使用对象
   * @param {Object} message - 对应的消息
   * @param {Object} options - 处理选项
   * @param {Object} context - 处理上下文
   * @returns {AsyncGenerator} 工具执行流生成器
   */
  async *executeToolUse(toolUse, message, options, context) {
    const toolName = toolUse.name;
    const tool = options.tools.find(t => t.name === toolName);

    // 标记工具为进行中状态
    context.setInProgressToolUseIDs(ids => new Set([...ids, toolUse.id]));

    if (!tool) {
      yield {
        type: MESSAGE_TYPES.TOOL_RESULT,
        toolUseId: toolUse.id,
        error: `工具不存在: ${toolName}`,
        timestamp: new Date().toISOString()
      };
      return;
    }

    try {
      logger.debug('开始执行工具', { toolName, toolUseId: toolUse.id });

      // 验证工具输入
      const validationResult = this.validateToolInput(tool, toolUse.input);
      
      if (!validationResult.success) {
        yield {
          type: MESSAGE_TYPES.TOOL_RESULT,
          toolUseId: toolUse.id,
          error: `工具输入验证失败: ${validationResult.error}`,
          timestamp: new Date().toISOString()
        };
        return;
      }

      // 执行工具
      const toolResult = await this.callTool(tool, toolUse.input, context);

      yield {
        type: MESSAGE_TYPES.TOOL_RESULT,
        toolUseId: toolUse.id,
        result: toolResult,
        timestamp: new Date().toISOString()
      };

      logTelemetryEvent('tool_executed', {
        toolName,
        success: true
      });

    } catch (error) {
      logger.error('工具执行失败', { 
        toolName, 
        toolUseId: toolUse.id, 
        error: error.message 
      });

      yield {
        type: MESSAGE_TYPES.TOOL_RESULT,
        toolUseId: toolUse.id,
        error: error.message,
        timestamp: new Date().toISOString()
      };

      logTelemetryEvent('tool_executed', {
        toolName,
        success: false,
        error: error.message
      });

    } finally {
      // 移除进行中状态
      context.setInProgressToolUseIDs(ids => {
        const newIds = new Set(ids);
        newIds.delete(toolUse.id);
        return newIds;
      });
    }
  }

  /**
   * 验证消息格式
   * @param {Array} messages - 消息数组
   * @throws {Error} 如果消息格式无效
   */
  validateMessages(messages) {
    if (!Array.isArray(messages)) {
      throw new Error('消息必须是数组格式');
    }

    for (const message of messages) {
      if (!message.role || !message.content) {
        throw new Error('消息必须包含role和content字段');
      }

      if (!Object.values(MESSAGE_TYPES).includes(message.role)) {
        throw new Error(`无效的消息角色: ${message.role}`);
      }
    }
  }

  /**
   * 处理系统提示
   * @param {Array} systemPrompts - 系统提示数组
   * @returns {Promise<Array>} 处理后的系统提示
   */
  async processSystemPrompts(systemPrompts) {
    try {
      const processedPrompts = [];

      for (const prompt of systemPrompts) {
        if (typeof prompt === 'string') {
          processedPrompts.push(prompt);
        } else if (typeof prompt === 'function') {
          const result = await prompt();
          processedPrompts.push(result);
        } else if (prompt && typeof prompt === 'object' && prompt.content) {
          processedPrompts.push(prompt.content);
        }
      }

      return processedPrompts;

    } catch (error) {
      logger.error('处理系统提示失败', { error: error.message });
      throw error;
    }
  }

  /**
   * 构建API请求
   * @param {Array} messages - 消息数组
   * @param {Array} systemPrompts - 系统提示数组
   * @param {Object} options - 选项
   * @returns {Promise<Object>} API请求对象
   */
  async buildAPIRequest(messages, systemPrompts, options) {
    try {
      const request = {
        model: options.model || getCurrentModel(),
        messages: this.formatMessagesForAPI(messages),
        max_tokens: options.maxTokens || 4096,
        temperature: options.temperature || 0.7,
        stream: true
      };

      // 添加系统提示
      if (systemPrompts.length > 0) {
        request.system = systemPrompts.join('\n\n');
      }

      // 添加工具定义
      if (options.tools && options.tools.length > 0) {
        request.tools = options.tools.map(tool => ({
          name: tool.name,
          description: tool.description,
          input_schema: tool.inputSchema
        }));
      }

      return request;

    } catch (error) {
      logger.error('构建API请求失败', { error: error.message });
      throw error;
    }
  }

  /**
   * 格式化消息为API格式
   * @param {Array} messages - 消息数组
   * @returns {Array} 格式化后的消息
   */
  formatMessagesForAPI(messages) {
    return messages.map(message => ({
      role: message.role,
      content: Array.isArray(message.content) ? message.content : [
        {
          type: 'text',
          text: message.content
        }
      ]
    }));
  }

  /**
   * 处理API响应流
   * @param {Object} apiRequest - API请求
   * @param {Object} context - 处理上下文
   * @returns {AsyncGenerator} API响应流生成器
   */
  async *handleAPIResponseStream(apiRequest, context) {
    try {
      // 这里需要实现实际的API调用逻辑
      // 模拟流式响应
      yield {
        type: MESSAGE_TYPES.ASSISTANT,
        content: '正在处理您的请求...',
        timestamp: new Date().toISOString()
      };

      // 模拟延迟
      await new Promise(resolve => setTimeout(resolve, 1000));

      yield {
        type: MESSAGE_TYPES.ASSISTANT,
        content: '请求处理完成',
        timestamp: new Date().toISOString()
      };

    } catch (error) {
      logger.error('处理API响应流失败', { error: error.message });
      throw error;
    }
  }

  /**
   * 验证工具输入
   * @param {Object} tool - 工具对象
   * @param {Object} input - 输入数据
   * @returns {Object} 验证结果
   */
  validateToolInput(tool, input) {
    try {
      // 这里可以实现JSON Schema验证
      if (tool.inputSchema) {
        // 简单验证示例
        const requiredFields = tool.inputSchema.required || [];
        
        for (const field of requiredFields) {
          if (!(field in input)) {
            return {
              success: false,
              error: `缺少必需字段: ${field}`
            };
          }
        }
      }

      return { success: true };

    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 调用工具
   * @param {Object} tool - 工具对象
   * @param {Object} input - 输入数据
   * @param {Object} context - 执行上下文
   * @returns {Promise<*>} 工具执行结果
   */
  async callTool(tool, input, context) {
    try {
      if (typeof tool.execute === 'function') {
        return await tool.execute(input, context);
      }

      throw new Error(`工具 ${tool.name} 没有可执行的函数`);

    } catch (error) {
      logger.error('调用工具失败', { 
        toolName: tool.name, 
        error: error.message 
      });

      throw error;
    }
  }

  /**
   * 注册消息处理器
   * @param {string} messageType - 消息类型
   * @param {Function} handler - 处理器函数
   */
  registerMessageHandler(messageType, handler) {
    if (!this.messageHandlers.has(messageType)) {
      this.messageHandlers.set(messageType, []);
    }
    
    this.messageHandlers.get(messageType).push(handler);
    
    logger.debug('消息处理器已注册', { messageType });
  }

  /**
   * 注册流处理器
   * @param {string} streamType - 流类型
   * @param {Function} handler - 处理器函数
   */
  registerStreamHandler(streamType, handler) {
    this.streamHandlers.set(streamType, handler);
    logger.debug('流处理器已注册', { streamType });
  }

  /**
   * 处理单个消息
   * @param {Object} message - 消息对象
   * @param {Object} context - 处理上下文
   * @returns {Promise<Object>} 处理结果
   */
  async processMessage(message, context = {}) {
    try {
      const handlers = this.messageHandlers.get(message.type) || [];
      
      let result = message;
      
      for (const handler of handlers) {
        result = await handler(result, context);
      }

      return result;

    } catch (error) {
      logger.error('处理消息失败', { 
        messageType: message.type, 
        error: error.message 
      });

      throw error;
    }
  }

  /**
   * 添加消息到队列
   * @param {Object} message - 消息对象
   * @param {number} priority - 优先级
   */
  enqueueMessage(message, priority = 0) {
    const queueItem = {
      message,
      priority,
      enqueuedAt: new Date().toISOString(),
      id: this.generateMessageId()
    };

    this.messageQueue.push(queueItem);
    
    // 按优先级排序
    this.messageQueue.sort((a, b) => b.priority - a.priority);

    logger.debug('消息已加入队列', { 
      messageId: queueItem.id,
      priority,
      queueLength: this.messageQueue.length
    });
  }

  /**
   * 从队列处理消息
   * @param {Object} context - 处理上下文
   * @returns {Promise<Object|null>} 处理结果
   */
  async dequeueAndProcessMessage(context = {}) {
    if (this.messageQueue.length === 0) {
      return null;
    }

    const queueItem = this.messageQueue.shift();
    
    try {
      this.processingQueue.add(queueItem.id);
      
      const result = await this.processMessage(queueItem.message, context);
      
      this.processingQueue.delete(queueItem.id);
      
      return {
        success: true,
        messageId: queueItem.id,
        result
      };

    } catch (error) {
      this.processingQueue.delete(queueItem.id);
      
      logger.error('队列消息处理失败', { 
        messageId: queueItem.id, 
        error: error.message 
      });

      return {
        success: false,
        messageId: queueItem.id,
        error: error.message
      };
    }
  }

  /**
   * 生成消息ID
   * @returns {string} 消息ID
   */
  generateMessageId() {
    return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 获取队列状态
   * @returns {Object} 队列状态
   */
  getQueueStatus() {
    return {
      queueLength: this.messageQueue.length,
      processingCount: this.processingQueue.size,
      inProgressToolUses: this.inProgressToolUseIDs.size
    };
  }

  /**
   * 清空消息队列
   */
  clearQueue() {
    this.messageQueue = [];
    this.processingQueue.clear();
    logger.debug('消息队列已清空');
  }
}

/**
 * 消息格式化器类
 */
export class MessageFormatter {
  constructor() {
    this.formatters = new Map();
    this.setupDefaultFormatters();
  }

  /**
   * 设置默认格式化器
   */
  setupDefaultFormatters() {
    // 用户消息格式化器
    this.formatters.set(MESSAGE_TYPES.USER, (message) => ({
      role: 'user',
      content: typeof message.content === 'string' ? 
        [{ type: 'text', text: message.content }] : 
        message.content
    }));

    // 助手消息格式化器
    this.formatters.set(MESSAGE_TYPES.ASSISTANT, (message) => ({
      role: 'assistant',
      content: typeof message.content === 'string' ? 
        [{ type: 'text', text: message.content }] : 
        message.content
    }));

    // 工具使用格式化器
    this.formatters.set(MESSAGE_TYPES.TOOL_USE, (message) => ({
      role: 'assistant',
      content: [{
        type: 'tool_use',
        id: message.id,
        name: message.name,
        input: message.input
      }]
    }));

    // 工具结果格式化器
    this.formatters.set(MESSAGE_TYPES.TOOL_RESULT, (message) => ({
      role: 'user',
      content: [{
        type: 'tool_result',
        tool_use_id: message.toolUseId,
        content: message.result || message.error,
        is_error: !!message.error
      }]
    }));
  }

  /**
   * 格式化消息
   * @param {Object} message - 原始消息
   * @returns {Object} 格式化后的消息
   */
  formatMessage(message) {
    const formatter = this.formatters.get(message.type);
    
    if (formatter) {
      return formatter(message);
    }

    // 默认格式化
    return {
      role: message.role || message.type,
      content: message.content
    };
  }

  /**
   * 批量格式化消息
   * @param {Array} messages - 消息数组
   * @returns {Array} 格式化后的消息数组
   */
  formatMessages(messages) {
    return messages.map(message => this.formatMessage(message));
  }

  /**
   * 注册自定义格式化器
   * @param {string} messageType - 消息类型
   * @param {Function} formatter - 格式化器函数
   */
  registerFormatter(messageType, formatter) {
    this.formatters.set(messageType, formatter);
    logger.debug('消息格式化器已注册', { messageType });
  }
}

/**
 * 消息验证器类
 */
export class MessageValidator {
  constructor() {
    this.validationRules = new Map();
    this.setupDefaultRules();
  }

  /**
   * 设置默认验证规则
   */
  setupDefaultRules() {
    // 用户消息验证规则
    this.validationRules.set(MESSAGE_TYPES.USER, (message) => {
      if (!message.content) {
        return { valid: false, error: '用户消息必须包含内容' };
      }
      return { valid: true };
    });

    // 工具使用验证规则
    this.validationRules.set(MESSAGE_TYPES.TOOL_USE, (message) => {
      if (!message.name) {
        return { valid: false, error: '工具使用必须指定工具名称' };
      }
      if (!message.id) {
        return { valid: false, error: '工具使用必须包含ID' };
      }
      return { valid: true };
    });
  }

  /**
   * 验证消息
   * @param {Object} message - 消息对象
   * @returns {Object} 验证结果
   */
  validateMessage(message) {
    try {
      const rule = this.validationRules.get(message.type);
      
      if (rule) {
        return rule(message);
      }

      // 默认验证
      return { valid: true };

    } catch (error) {
      return {
        valid: false,
        error: error.message
      };
    }
  }

  /**
   * 批量验证消息
   * @param {Array} messages - 消息数组
   * @returns {Object} 验证结果
   */
  validateMessages(messages) {
    const results = [];
    let allValid = true;

    for (let i = 0; i < messages.length; i++) {
      const result = this.validateMessage(messages[i]);
      result.index = i;
      results.push(result);
      
      if (!result.valid) {
        allValid = false;
      }
    }

    return {
      valid: allValid,
      results,
      invalidCount: results.filter(r => !r.valid).length
    };
  }

  /**
   * 注册验证规则
   * @param {string} messageType - 消息类型
   * @param {Function} rule - 验证规则函数
   */
  registerValidationRule(messageType, rule) {
    this.validationRules.set(messageType, rule);
    logger.debug('消息验证规则已注册', { messageType });
  }
}

/**
 * 全局消息处理器实例
 */
export const globalMessageProcessor = new MessageProcessor();

/**
 * 全局消息格式化器实例
 */
export const globalMessageFormatter = new MessageFormatter();

/**
 * 全局消息验证器实例
 */
export const globalMessageValidator = new MessageValidator();

/**
 * 便捷函数：处理消息流
 * @param {string} sessionId - 会话ID
 * @param {Array} messages - 消息数组
 * @param {Object} options - 处理选项
 * @param {Object} context - 处理上下文
 * @returns {AsyncGenerator} 消息流生成器
 */
export async function* processMessageStream(sessionId, messages, options, context) {
  yield* globalMessageProcessor.processMessageStream(sessionId, messages, options, context);
}

/**
 * 便捷函数：格式化消息
 * @param {Array} messages - 消息数组
 * @returns {Array} 格式化后的消息数组
 */
export function formatMessages(messages) {
  return globalMessageFormatter.formatMessages(messages);
}

/**
 * 便捷函数：验证消息
 * @param {Array} messages - 消息数组
 * @returns {Object} 验证结果
 */
export function validateMessages(messages) {
  return globalMessageValidator.validateMessages(messages);
}

/**
 * 便捷函数：添加消息到队列
 * @param {Object} message - 消息对象
 * @param {number} priority - 优先级
 */
export function enqueueMessage(message, priority = 0) {
  globalMessageProcessor.enqueueMessage(message, priority);
}
