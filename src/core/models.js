/**
 * 模型管理系统
 * 
 * 管理Claude模型的选择、配置、使用统计和回退机制
 * 
 * @original: 模型管理相关代码 (L2013-2081, L5525-5534, L5635-5655, L5684-5729)
 */

import { logger } from '../utils/logger.js';
import { getConfig, setConfig } from '../config/settings.js';
import { logTelemetryEvent } from '../services/telemetry.js';

/**
 * 支持的模型列表
 * @original: 模型相关常量和配置
 */
export const SUPPORTED_MODELS = {
  // Claude 4系列
  SONNET_4: 'claude-sonnet-4-20250514',
  OPUS_4_1: 'claude-opus-4-1',
  OPUS_4: 'claude-opus-4-0',
  
  // Claude 3.7系列
  SONNET_3_7: 'claude-3-7-sonnet',
  
  // Claude 3.5系列
  SONNET_3_5: 'claude-3-5-sonnet-20241022',
  HAIKU_3_5: 'claude-3-5-haiku-20241022',
  
  // 已废弃模型
  CLAUDE_2_1: 'claude-2.1',
  CLAUDE_2_0: 'claude-2.0',
  CLAUDE_1_3: 'claude-1.3',
  INSTANT_1_1: 'claude-instant-1.1',
  INSTANT_1_2: 'claude-instant-1.2'
};

/**
 * 模型显示名称映射
 * @original: YsA()函数 (L5525-5534)
 */
export const MODEL_DISPLAY_NAMES = {
  [SUPPORTED_MODELS.SONNET_4]: 'Sonnet 4',
  [SUPPORTED_MODELS.OPUS_4_1]: 'Opus 4.1',
  [SUPPORTED_MODELS.OPUS_4]: 'Opus 4',
  [SUPPORTED_MODELS.SONNET_3_7]: 'Claude 3.7 Sonnet',
  [SUPPORTED_MODELS.SONNET_3_5]: 'Claude 3.5 Sonnet',
  [SUPPORTED_MODELS.HAIKU_3_5]: 'Claude 3.5 Haiku'
};

/**
 * 已废弃模型及其EOL日期
 * @original: P8B对象 (L12414-12422)
 */
export const DEPRECATED_MODELS = {
  [SUPPORTED_MODELS.CLAUDE_1_3]: 'November 6th, 2024',
  [SUPPORTED_MODELS.INSTANT_1_1]: 'November 6th, 2024',
  [SUPPORTED_MODELS.INSTANT_1_2]: 'November 6th, 2024',
  [SUPPORTED_MODELS.CLAUDE_2_1]: 'July 21st, 2025',
  [SUPPORTED_MODELS.CLAUDE_2_0]: 'July 21st, 2025'
};

/**
 * 模型最大输出令牌数
 * @original: fj1对象 (L12407-12412)
 */
export const MODEL_MAX_OUTPUT_TOKENS = {
  [SUPPORTED_MODELS.OPUS_4]: 8192,
  [SUPPORTED_MODELS.OPUS_4_1]: 8192,
  [SUPPORTED_MODELS.SONNET_4]: 8192
};

/**
 * 模型使用统计类
 */
export class ModelUsageTracker {
  constructor() {
    this.modelUsage = {};
    this.totalCostUSD = 0;
    this.totalAPIDuration = 0;
    this.totalAPIDurationWithoutRetries = 0;
    this.hasUnknownModelCost = false;
    this.initialMainLoopModel = null;
    this.mainLoopModelOverride = null;
  }

  /**
   * 记录模型使用情况
   * @original: Pj0()函数 (L2011-2021)
   * @param {number} costUSD - 成本（美元）
   * @param {number} apiDuration - API持续时间
   * @param {number} apiDurationWithoutRetries - 不含重试的API持续时间
   * @param {Object} usage - 使用统计
   * @param {string} modelName - 模型名称
   */
  recordModelUsage(costUSD, apiDuration, apiDurationWithoutRetries, usage, modelName) {
    this.totalCostUSD += costUSD;
    this.totalAPIDuration += apiDuration;
    this.totalAPIDurationWithoutRetries += apiDurationWithoutRetries;

    let modelStats = this.modelUsage[modelName] ?? {
      inputTokens: 0,
      outputTokens: 0,
      cacheReadInputTokens: 0,
      cacheCreationInputTokens: 0,
      webSearchRequests: 0
    };

    modelStats.inputTokens += usage.input_tokens;
    modelStats.outputTokens += usage.output_tokens;
    modelStats.cacheReadInputTokens += usage.cache_read_input_tokens ?? 0;
    modelStats.cacheCreationInputTokens += usage.cache_creation_input_tokens ?? 0;
    modelStats.webSearchRequests += usage.server_tool_use?.web_search_requests ?? 0;

    this.modelUsage[modelName] = modelStats;

    logger.debug('模型使用情况已记录', {
      modelName,
      costUSD,
      inputTokens: usage.input_tokens,
      outputTokens: usage.output_tokens
    });
  }

  /**
   * 获取总输入令牌数
   * @original: Sj0()函数 (L2044)
   * @returns {number} 总输入令牌数
   */
  getTotalInputTokens() {
    return this.sumModelUsageField('inputTokens');
  }

  /**
   * 获取总输出令牌数
   * @original: jj0()函数 (L2047)
   * @returns {number} 总输出令牌数
   */
  getTotalOutputTokens() {
    return this.sumModelUsageField('outputTokens');
  }

  /**
   * 获取总缓存读取令牌数
   * @original: yj0()函数 (L2050)
   * @returns {number} 总缓存读取令牌数
   */
  getTotalCacheReadTokens() {
    return this.sumModelUsageField('cacheReadInputTokens');
  }

  /**
   * 获取总缓存创建令牌数
   * @original: kj0()函数 (L2053)
   * @returns {number} 总缓存创建令牌数
   */
  getTotalCacheCreationTokens() {
    return this.sumModelUsageField('cacheCreationInputTokens');
  }

  /**
   * 获取总网络搜索请求数
   * @original: _j0()函数 (L2056)
   * @returns {number} 总网络搜索请求数
   */
  getTotalWebSearchRequests() {
    return this.sumModelUsageField('webSearchRequests');
  }

  /**
   * 汇总模型使用字段
   * @param {string} field - 字段名
   * @returns {number} 汇总值
   */
  sumModelUsageField(field) {
    return Object.values(this.modelUsage).reduce((sum, usage) => sum + (usage[field] || 0), 0);
  }

  /**
   * 标记未知模型成本
   * @original: du1()函数 (L2059)
   */
  markUnknownModelCost() {
    this.hasUnknownModelCost = true;
  }

  /**
   * 检查是否有未知模型成本
   * @original: xj0()函数 (L2062)
   * @returns {boolean} 是否有未知模型成本
   */
  hasUnknownCost() {
    return this.hasUnknownModelCost;
  }

  /**
   * 获取模型使用统计
   * @original: vj0()函数 (L2068)
   * @returns {Object} 模型使用统计
   */
  getModelUsage() {
    return this.modelUsage;
  }

  /**
   * 获取初始主循环模型
   * @original: CW1()函数 (L2071)
   * @returns {string|null} 初始主循环模型
   */
  getInitialMainLoopModel() {
    return this.initialMainLoopModel;
  }

  /**
   * 设置主循环模型覆盖
   * @original: sA1()函数 (L2074)
   * @param {string} model - 模型名称
   */
  setMainLoopModelOverride(model) {
    this.mainLoopModelOverride = model;
  }

  /**
   * 设置初始主循环模型
   * @original: fj0()函数 (L2080)
   * @param {string} model - 模型名称
   */
  setInitialMainLoopModel(model) {
    this.initialMainLoopModel = model;
  }

  /**
   * 重置使用统计
   */
  reset() {
    this.modelUsage = {};
    this.totalCostUSD = 0;
    this.totalAPIDuration = 0;
    this.totalAPIDurationWithoutRetries = 0;
    this.hasUnknownModelCost = false;
  }
}

/**
 * 模型管理器类
 */
export class ModelManager {
  constructor() {
    this.usageTracker = new ModelUsageTracker();
    this.currentModel = null;
    this.fallbackModel = null;
    this.modelAliases = new Map();
    this.setupModelAliases();
  }

  /**
   * 设置模型别名
   */
  setupModelAliases() {
    this.modelAliases.set('sonnet', SUPPORTED_MODELS.SONNET_4);
    this.modelAliases.set('opus', SUPPORTED_MODELS.OPUS_4_1);
    this.modelAliases.set('haiku', SUPPORTED_MODELS.HAIKU_3_5);
    this.modelAliases.set('sonnet-3.5', SUPPORTED_MODELS.SONNET_3_5);
    this.modelAliases.set('haiku-3.5', SUPPORTED_MODELS.HAIKU_3_5);
  }

  /**
   * 获取模型显示名称
   * @original: YsA()函数 (L5525-5534)
   * @param {string} modelName - 模型名称
   * @returns {string|undefined} 显示名称
   */
  getModelDisplayName(modelName) {
    const normalizedName = modelName.toLowerCase();
    
    if (normalizedName.includes('claude-sonnet-4')) return 'Sonnet 4';
    if (normalizedName.includes('claude-opus-4-1')) return 'Opus 4.1';
    if (normalizedName.includes('claude-opus-4')) return 'Opus 4';
    if (normalizedName.includes('claude-3-7-sonnet')) return 'Claude 3.7 Sonnet';
    if (normalizedName.includes('claude-3-5-sonnet')) return 'Claude 3.5 Sonnet';
    if (normalizedName.includes('claude-3-5-haiku')) return 'Claude 3.5 Haiku';
    
    return MODEL_DISPLAY_NAMES[modelName];
  }

  /**
   * 解析模型名称
   * @original: aw()函数 (L5651-5655)
   * @param {string} modelName - 模型名称
   * @returns {string} 解析后的模型名称
   */
  parseModelName(modelName) {
    if (modelName.includes('claude-opus-4-1')) return 'claude-opus-4-1';
    if (modelName.includes('claude-opus-4')) return 'claude-opus-4';
    
    const match = modelName.match(/(claude-(\d+-\d+-)?\w+)/);
    if (match && match[1]) return match[1];
    
    return modelName;
  }

  /**
   * 获取默认模型
   * @original: og()函数 (L5635)
   * @returns {string} 默认模型名称
   */
  getDefaultModel() {
    return process.env.ANTHROPIC_SMALL_FAST_MODEL || SUPPORTED_MODELS.HAIKU_3_5;
  }

  /**
   * 获取子代理模型
   * @original: ow2()函数 (L5745-5747)
   * @param {string} subagentModel - 子代理模型配置
   * @param {string} parentModel - 父模型
   * @returns {string} 子代理模型名称
   */
  getSubagentModel(subagentModel, parentModel) {
    if (process.env.CLAUDE_CODE_SUBAGENT_MODEL) {
      return process.env.CLAUDE_CODE_SUBAGENT_MODEL;
    }
    
    if (!subagentModel) {
      return this.resolveModelAlias(this.getDefaultModel());
    }
    
    return subagentModel === 'inherit' ? parentModel : this.resolveModelAlias(subagentModel);
  }

  /**
   * 解析模型别名
   * @param {string} modelAlias - 模型别名或完整名称
   * @returns {string} 解析后的模型名称
   */
  resolveModelAlias(modelAlias) {
    if (this.modelAliases.has(modelAlias)) {
      return this.modelAliases.get(modelAlias);
    }
    
    return modelAlias;
  }

  /**
   * 获取模型选项列表
   * @original: 模型选项生成逻辑 (L5684-5729)
   * @param {Object} options - 选项配置
   * @returns {Array} 模型选项列表
   */
  getModelOptions(options = {}) {
    const { forDisplay = false, includeCustom = true } = options;
    
    const modelOptions = [
      {
        value: null,
        label: 'Default (recommended)',
        description: `Use the default model (currently ${this.getModelDisplayName(this.getDefaultModel())}) · $3/$15 per Mtok`
      },
      {
        value: SUPPORTED_MODELS.SONNET_4,
        label: 'Sonnet 4',
        description: 'Most capable model · $3/$15 per Mtok'
      },
      {
        value: SUPPORTED_MODELS.OPUS_4_1,
        label: 'Opus 4.1',
        description: 'Advanced reasoning · $15/$75 per Mtok'
      },
      {
        value: SUPPORTED_MODELS.HAIKU_3_5,
        label: 'Haiku 3.5',
        description: 'Fast and efficient · $0.25/$1.25 per Mtok'
      }
    ];

    if (includeCustom) {
      modelOptions.push({
        value: 'custom',
        label: 'Custom model',
        description: 'Custom model'
      });
    }

    return modelOptions;
  }

  /**
   * 获取子代理模型选项
   * @original: 子代理模型选项逻辑 (L5770-5774)
   * @returns {Array} 子代理模型选项
   */
  getSubagentModelOptions() {
    const options = this.getModelOptions({ includeCustom: false });
    
    // 添加继承选项
    options.unshift({
      value: 'inherit',
      label: 'Inherit from parent',
      description: 'Use the same model as the main conversation'
    });

    return options;
  }

  /**
   * 检查模型是否已废弃
   * @param {string} modelName - 模型名称
   * @returns {boolean} 是否已废弃
   */
  isModelDeprecated(modelName) {
    return modelName in DEPRECATED_MODELS;
  }

  /**
   * 获取模型废弃信息
   * @param {string} modelName - 模型名称
   * @returns {Object|null} 废弃信息
   */
  getDeprecationInfo(modelName) {
    if (!this.isModelDeprecated(modelName)) {
      return null;
    }

    return {
      model: modelName,
      eolDate: DEPRECATED_MODELS[modelName],
      message: `The model '${modelName}' is deprecated and will reach end-of-life on ${DEPRECATED_MODELS[modelName]}`
    };
  }

  /**
   * 显示废弃警告
   * @original: 废弃警告逻辑 (L12433-12434)
   * @param {string} modelName - 模型名称
   */
  showDeprecationWarning(modelName) {
    const deprecationInfo = this.getDeprecationInfo(modelName);
    
    if (deprecationInfo) {
      console.warn(`${deprecationInfo.message}\nPlease migrate to a newer model. Visit https://docs.anthropic.com/en/docs/resources/model-deprecations for more information.`);
    }
  }

  /**
   * 获取当前模型
   * @returns {string|null} 当前模型
   */
  getCurrentModel() {
    return this.mainLoopModelOverride || this.initialMainLoopModel;
  }

  /**
   * 设置当前模型
   * @param {string} modelName - 模型名称
   */
  setCurrentModel(modelName) {
    const resolvedModel = this.resolveModelAlias(modelName);
    this.currentModel = resolvedModel;
    this.usageTracker.setMainLoopModelOverride(resolvedModel);
    
    // 保存到配置
    setConfig('model', resolvedModel);
    
    logger.info('当前模型已设置', { modelName: resolvedModel });
  }

  /**
   * 设置回退模型
   * @param {string} modelName - 回退模型名称
   */
  setFallbackModel(modelName) {
    this.fallbackModel = this.resolveModelAlias(modelName);
    logger.info('回退模型已设置', { fallbackModel: this.fallbackModel });
  }

  /**
   * 获取模型成本信息
   * @param {string} modelName - 模型名称
   * @returns {Object} 成本信息
   */
  getModelCostInfo(modelName) {
    // 这里可以添加具体的成本计算逻辑
    const costMap = {
      [SUPPORTED_MODELS.SONNET_4]: { input: 3, output: 15 },
      [SUPPORTED_MODELS.OPUS_4_1]: { input: 15, output: 75 },
      [SUPPORTED_MODELS.OPUS_4]: { input: 15, output: 75 },
      [SUPPORTED_MODELS.HAIKU_3_5]: { input: 0.25, output: 1.25 }
    };

    return costMap[modelName] || { input: 0, output: 0 };
  }

  /**
   * 验证模型可用性
   * @param {string} modelName - 模型名称
   * @returns {Promise<boolean>} 模型是否可用
   */
  async validateModelAvailability(modelName) {
    try {
      // 这里可以添加实际的模型可用性检查逻辑
      // 比如调用API检查模型是否存在
      
      if (this.isModelDeprecated(modelName)) {
        logger.warn('使用已废弃的模型', { modelName });
        this.showDeprecationWarning(modelName);
      }

      return true;

    } catch (error) {
      logger.error('模型可用性验证失败', { modelName, error: error.message });
      return false;
    }
  }

  /**
   * 获取使用统计摘要
   * @returns {Object} 使用统计摘要
   */
  getUsageSummary() {
    return {
      totalCost: this.usageTracker.totalCostUSD,
      totalInputTokens: this.usageTracker.getTotalInputTokens(),
      totalOutputTokens: this.usageTracker.getTotalOutputTokens(),
      totalCacheReadTokens: this.usageTracker.getTotalCacheReadTokens(),
      totalCacheCreationTokens: this.usageTracker.getTotalCacheCreationTokens(),
      totalWebSearchRequests: this.usageTracker.getTotalWebSearchRequests(),
      modelUsage: this.usageTracker.getModelUsage(),
      hasUnknownCost: this.usageTracker.hasUnknownCost()
    };
  }
}

/**
 * 模型回退管理器类
 */
export class ModelFallbackManager {
  constructor(modelManager) {
    this.modelManager = modelManager || new ModelManager();
    this.fallbackHistory = [];
    this.maxFallbackAttempts = 3;
  }

  /**
   * 执行模型回退
   * @param {string} originalModel - 原始模型
   * @param {string} reason - 回退原因
   * @returns {Promise<string>} 回退后的模型
   */
  async performFallback(originalModel, reason = 'rate_limit') {
    try {
      const fallbackModel = this.determineFallbackModel(originalModel, reason);
      
      if (!fallbackModel) {
        throw new Error('没有可用的回退模型');
      }

      // 记录回退历史
      this.fallbackHistory.push({
        originalModel,
        fallbackModel,
        reason,
        timestamp: new Date().toISOString()
      });

      logger.warn('执行模型回退', {
        originalModel,
        fallbackModel,
        reason
      });

      logTelemetryEvent('model_fallback', {
        originalModel,
        fallbackModel,
        reason
      });

      return fallbackModel;

    } catch (error) {
      logger.error('模型回退失败', { originalModel, reason, error: error.message });
      throw error;
    }
  }

  /**
   * 确定回退模型
   * @param {string} originalModel - 原始模型
   * @param {string} reason - 回退原因
   * @returns {string|null} 回退模型
   */
  determineFallbackModel(originalModel, reason) {
    // 如果已设置回退模型，使用它
    if (this.modelManager.fallbackModel) {
      return this.modelManager.fallbackModel;
    }

    // 根据原始模型和原因确定回退策略
    switch (reason) {
      case 'rate_limit':
        return this.getRateLimitFallback(originalModel);
      
      case 'unavailable':
        return this.getUnavailableFallback(originalModel);
      
      case 'error':
        return this.getErrorFallback(originalModel);
      
      default:
        return SUPPORTED_MODELS.HAIKU_3_5; // 默认回退到最快的模型
    }
  }

  /**
   * 获取速率限制回退模型
   * @param {string} originalModel - 原始模型
   * @returns {string} 回退模型
   */
  getRateLimitFallback(originalModel) {
    // 从高级模型回退到低级模型
    if (originalModel.includes('opus')) {
      return SUPPORTED_MODELS.SONNET_4;
    }
    
    if (originalModel.includes('sonnet-4')) {
      return SUPPORTED_MODELS.SONNET_3_5;
    }
    
    return SUPPORTED_MODELS.HAIKU_3_5;
  }

  /**
   * 获取不可用回退模型
   * @param {string} originalModel - 原始模型
   * @returns {string} 回退模型
   */
  getUnavailableFallback(originalModel) {
    // 回退到稳定的模型
    return SUPPORTED_MODELS.SONNET_3_5;
  }

  /**
   * 获取错误回退模型
   * @param {string} originalModel - 原始模型
   * @returns {string} 回退模型
   */
  getErrorFallback(originalModel) {
    // 回退到最稳定的模型
    return SUPPORTED_MODELS.HAIKU_3_5;
  }

  /**
   * 获取回退历史
   * @returns {Array} 回退历史
   */
  getFallbackHistory() {
    return this.fallbackHistory;
  }

  /**
   * 清除回退历史
   */
  clearFallbackHistory() {
    this.fallbackHistory = [];
  }
}

/**
 * 全局模型使用跟踪器实例
 */
export const globalModelUsageTracker = new ModelUsageTracker();

/**
 * 全局模型管理器实例
 */
export const globalModelManager = new ModelManager();

/**
 * 全局模型回退管理器实例
 */
export const globalModelFallbackManager = new ModelFallbackManager(globalModelManager);

/**
 * 便捷函数：记录模型使用
 * @param {number} costUSD - 成本
 * @param {number} apiDuration - API持续时间
 * @param {number} apiDurationWithoutRetries - 不含重试的API持续时间
 * @param {Object} usage - 使用统计
 * @param {string} modelName - 模型名称
 */
export function recordModelUsage(costUSD, apiDuration, apiDurationWithoutRetries, usage, modelName) {
  globalModelUsageTracker.recordModelUsage(costUSD, apiDuration, apiDurationWithoutRetries, usage, modelName);
}

/**
 * 便捷函数：获取模型显示名称
 * @param {string} modelName - 模型名称
 * @returns {string} 显示名称
 */
export function getModelDisplayName(modelName) {
  return globalModelManager.getModelDisplayName(modelName) || modelName;
}

/**
 * 便捷函数：设置当前模型
 * @param {string} modelName - 模型名称
 */
export function setCurrentModel(modelName) {
  globalModelManager.setCurrentModel(modelName);
}

/**
 * 便捷函数：获取当前模型
 * @returns {string|null} 当前模型
 */
export function getCurrentModel() {
  return globalModelManager.getCurrentModel();
}

/**
 * 便捷函数：执行模型回退
 * @param {string} originalModel - 原始模型
 * @param {string} reason - 回退原因
 * @returns {Promise<string>} 回退后的模型
 */
export async function performModelFallback(originalModel, reason = 'rate_limit') {
  return globalModelFallbackManager.performFallback(originalModel, reason);
}

/**
 * 便捷函数：获取使用统计摘要
 * @returns {Object} 使用统计摘要
 */
export function getModelUsageSummary() {
  return globalModelManager.getUsageSummary();
}
