/**
 * 会话管理和状态跟踪
 * 
 * 负责管理Claude Code会话状态，包括成本跟踪、模型使用统计、会话ID管理等
 * 
 * @original: 行2002-2175的会话管理函数
 */

import { randomUUID } from 'crypto';
import { logger } from '../utils/logger.js';

/**
 * 全局会话状态对象
 * @original: i2对象
 */
const sessionState = {
  sessionId: null,
  cwd: process.cwd(),
  startTime: Date.now(),
  lastInteractionTime: Date.now(),
  totalCostUSD: 0,
  totalAPIDuration: 0,
  totalAPIDurationWithoutRetries: 0,
  totalLinesAdded: 0,
  totalLinesRemoved: 0,
  modelUsage: {},
  hasUnknownModelCost: false,
  initialMainLoopModel: null,
  mainLoopModelOverride: null,
  maxRateLimitFallbackActive: false,
  backgroundShells: new Map(),
  backgroundShellSubscribers: new Set(),
  backgroundShellCounter: 0,
  agentColorMap: new Map(),
  agentColorIndex: 0,
  isNonInteractiveSession: false,
  isInteractive: true,
  clientType: 'cli',
  flagSettingsPath: null,
  // 遥测相关
  meter: null,
  sessionCounter: null,
  locCounter: null,
  prCounter: null,
  commitCounter: null,
  costCounter: null,
  tokenCounter: null,
  codeEditToolDecisionCounter: null,
  activeTimeCounter: null,
  loggerProvider: null,
  eventLogger: null
};

/**
 * 生成新的会话ID
 * @original: Mj0()函数 (L2002)
 * @returns {string} 新的会话ID
 */
export function generateSessionId() {
  sessionState.sessionId = randomUUID();
  return sessionState.sessionId;
}

/**
 * 设置会话ID
 * @original: Rj0()函数 (L2005)
 * @param {string} sessionId - 会话ID
 */
export function setSessionId(sessionId) {
  sessionState.sessionId = sessionId;
}

/**
 * 设置当前工作目录
 * @original: Tj0()函数 (L2008)
 * @param {string} cwd - 工作目录路径
 */
export function setCurrentWorkingDirectory(cwd) {
  sessionState.cwd = cwd;
}

/**
 * 跟踪API使用情况
 * @original: Pj0()函数 (L2011)
 * @param {number} costUSD - 成本（美元）
 * @param {number} duration - 持续时间
 * @param {number} durationWithoutRetries - 不含重试的持续时间
 * @param {Object} usage - 使用统计
 * @param {string} modelName - 模型名称
 */
export async function trackApiUsage(costUSD, duration, durationWithoutRetries, usage, modelName) {
  sessionState.totalCostUSD += costUSD;
  sessionState.totalAPIDuration += duration;
  sessionState.totalAPIDurationWithoutRetries += durationWithoutRetries;

  let modelUsage = sessionState.modelUsage[modelName] ?? {
    inputTokens: 0,
    outputTokens: 0,
    cacheReadInputTokens: 0,
    cacheCreationInputTokens: 0,
    webSearchRequests: 0
  };

  modelUsage.inputTokens += usage.input_tokens;
  modelUsage.outputTokens += usage.output_tokens;
  modelUsage.cacheReadInputTokens += usage.cache_read_input_tokens ?? 0;
  modelUsage.cacheCreationInputTokens += usage.cache_creation_input_tokens ?? 0;
  modelUsage.webSearchRequests += usage.server_tool_use?.web_search_requests ?? 0;

  sessionState.modelUsage[modelName] = modelUsage;
}

/**
 * 获取总成本
 * @original: aq()函数 (L2022)
 * @returns {number} 总成本（美元）
 */
export function getTotalCost() {
  return sessionState.totalCostUSD;
}

/**
 * 获取总API持续时间
 * @original: fj()函数 (L2025)
 * @returns {number} 总持续时间
 */
export function getTotalAPIDuration() {
  return sessionState.totalAPIDuration;
}

/**
 * 获取会话运行时间
 * @original: uu1()函数 (L2028)
 * @returns {number} 运行时间（毫秒）
 */
export function getSessionUptime() {
  return Date.now() - sessionState.startTime;
}

/**
 * 更新最后交互时间
 * @original: nA1()函数 (L2031)
 */
export function updateLastInteractionTime() {
  sessionState.lastInteractionTime = Date.now();
}

/**
 * 跟踪代码行数变化
 * @original: mu1()函数 (L2034)
 * @param {number} linesAdded - 添加的行数
 * @param {number} linesRemoved - 删除的行数
 */
export function trackLinesChanged(linesAdded, linesRemoved) {
  sessionState.totalLinesAdded += linesAdded;
  sessionState.totalLinesRemoved += linesRemoved;
}

/**
 * 获取总添加行数
 * @original: JW1()函数 (L2037)
 * @returns {number} 总添加行数
 */
export function getTotalLinesAdded() {
  return sessionState.totalLinesAdded;
}

/**
 * 获取总删除行数
 * @original: XW1()函数 (L2040)
 * @returns {number} 总删除行数
 */
export function getTotalLinesRemoved() {
  return sessionState.totalLinesRemoved;
}

/**
 * 获取模型使用统计
 * @original: vj0()函数 (L2067)
 * @returns {Object} 模型使用统计
 */
export function getModelUsage() {
  return sessionState.modelUsage;
}

/**
 * 获取初始主循环模型
 * @original: CW1()函数 (L2070)
 * @returns {string|null} 初始主循环模型
 */
export function getInitialMainLoopModel() {
  return sessionState.initialMainLoopModel;
}

/**
 * 设置主循环模型覆盖
 * @original: sA1()函数 (L2073)
 * @param {string|null} model - 模型名称
 */
export function setMainLoopModelOverride(model) {
  sessionState.mainLoopModelOverride = model;
}

/**
 * 设置速率限制回退状态
 * @original: bj0()函数 (L2076)
 * @param {boolean} active - 是否激活
 */
export function setMaxRateLimitFallbackActive(active) {
  sessionState.maxRateLimitFallbackActive = active;
}

/**
 * 设置初始主循环模型
 * @original: fj0()函数 (L2079)
 * @param {string|null} model - 模型名称
 */
export function setInitialMainLoopModel(model) {
  sessionState.initialMainLoopModel = model;
}

/**
 * 初始化全局状态
 * 设置默认值并准备会话
 */
export function initializeGlobalState() {
  if (!sessionState.sessionId) {
    generateSessionId();
  }
  
  sessionState.startTime = Date.now();
  sessionState.lastInteractionTime = Date.now();
  
  logger.debug('会话状态已初始化', {
    sessionId: sessionState.sessionId,
    startTime: new Date(sessionState.startTime).toISOString()
  });
}

/**
 * 获取完整的会话状态（用于调试）
 * @returns {Object} 会话状态的副本
 */
export function getSessionState() {
  return { ...sessionState };
}

/**
 * 会话类型枚举
 */
export const SESSION_TYPES = {
  LOCAL: 'local',
  REMOTE: 'remote',
  TELEPORT: 'teleport'
};

/**
 * 会话状态枚举
 */
export const SESSION_STATUS = {
  ACTIVE: 'active',
  PAUSED: 'paused',
  COMPLETED: 'completed',
  ERROR: 'error'
};

/**
 * 扩展会话管理器类
 * @original: 会话管理相关代码 (L56115-56386, L57536-57540)
 */
export class ExtendedSessionManager {
  constructor(options = {}) {
    this.maxSessions = options.maxSessions || 100;
    this.sessionTimeout = options.sessionTimeout || 3600000; // 1小时
    this.autoSave = options.autoSave !== false;
    this.saveInterval = options.saveInterval || 30000; // 30秒

    this.sessions = new Map();
    this.currentSessionId = sessionState.sessionId;
    this.saveTimer = null;
  }

  /**
   * 创建新会话
   * @param {Object} options - 会话选项
   * @returns {Promise<Object>} 创建的会话
   */
  async createSession(options = {}) {
    const sessionId = options.sessionId || generateSessionId();

    const session = {
      id: sessionId,
      type: options.type || SESSION_TYPES.LOCAL,
      status: SESSION_STATUS.ACTIVE,
      title: options.title || this.generateSessionTitle(options.description),
      description: options.description || '',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      messages: [],
      metadata: {
        model: options.model,
        permissionMode: options.permissionMode,
        tools: options.tools || [],
        commands: options.commands || [],
        repository: options.repository,
        workingDirectory: options.workingDirectory || process.cwd(),
        ...options.metadata
      },
      usage: {
        inputTokens: 0,
        outputTokens: 0,
        cost: 0,
        apiDuration: 0,
        turns: 0
      }
    };

    this.sessions.set(sessionId, session);
    this.currentSessionId = sessionId;
    setSessionId(sessionId);

    logger.info('扩展会话已创建', {
      sessionId,
      type: session.type,
      title: session.title
    });

    return session;
  }

  /**
   * 生成会话标题
   * @param {string} description - 会话描述
   * @returns {string} 生成的标题
   */
  generateSessionTitle(description) {
    if (!description) {
      return `Session ${new Date().toLocaleString()}`;
    }

    const words = description.split(' ').slice(0, 6);
    let title = words.join(' ');

    if (title.length > 50) {
      title = title.substring(0, 47) + '...';
    }

    return title;
  }

  /**
   * 获取当前会话
   * @returns {Object|null} 当前会话
   */
  getCurrentSession() {
    if (!this.currentSessionId) {
      return null;
    }

    return this.sessions.get(this.currentSessionId);
  }

  /**
   * 获取会话列表
   * @param {Object} filters - 过滤条件
   * @returns {Array} 会话列表
   */
  getSessions(filters = {}) {
    let sessions = Array.from(this.sessions.values());

    if (filters.type) {
      sessions = sessions.filter(session => session.type === filters.type);
    }

    if (filters.status) {
      sessions = sessions.filter(session => session.status === filters.status);
    }

    sessions.sort((a, b) => new Date(b.updatedAt) - new Date(a.updatedAt));
    return sessions;
  }
}

/**
 * 全局扩展会话管理器实例
 */
export const globalExtendedSessionManager = new ExtendedSessionManager();
