/**
 * 权限管理系统
 * 
 * 负责管理工具权限、规则验证、权限决策等核心功能
 * 
 * @original: 行2969-4500的权限管理代码
 */

import { logger } from '../utils/logger.js';
import { PERMISSION_MODES } from '../config/constants.js';

/**
 * 权限行为枚举
 */
export const PERMISSION_BEHAVIORS = {
  ALLOW: 'allow',
  DENY: 'deny',
  ASK: 'ask',
  PASSTHROUGH: 'passthrough'
};

/**
 * 权限决策原因类型
 */
export const DECISION_REASON_TYPES = {
  RULE: 'rule',
  MODE: 'mode',
  HOOK: 'hook',
  OTHER: 'other',
  SUBCOMMAND_RESULTS: 'subcommandResults',
  PERMISSION_PROMPT_TOOL: 'permissionPromptTool'
};

/**
 * 权限模式映射
 * @original: QYA()函数 (L4434)
 */
export function mapPermissionMode(mode) {
  switch (mode) {
    case 'bypassPermissions':
      return 'bypassPermissions';
    case 'acceptEdits':
      return 'acceptEdits';
    case 'plan':
      return 'plan';
    default:
      return 'default';
  }
}

/**
 * 获取权限模式显示名称
 * @original: 相关显示名称函数 (L4451-4458)
 */
export function getPermissionModeDisplayName(mode) {
  switch (mode) {
    case 'plan':
      return 'Plan Mode';
    case 'acceptEdits':
      return 'Accept Edits';
    case 'bypassPermissions':
      return 'Bypass Permissions';
    default:
      return 'Default';
  }
}

/**
 * 获取权限模式简短名称
 * @original: jr1()函数 (L4459)
 */
export function getPermissionModeShortName(mode) {
  switch (mode) {
    case 'plan':
      return 'Plan';
    case 'acceptEdits':
      return 'Accept';
    case 'bypassPermissions':
      return 'Bypass';
    default:
      return 'Default';
  }
}

/**
 * 权限上下文类
 * 管理权限状态和规则
 * 
 * @original: vV()函数和相关权限上下文管理 (L8343-8347)
 */
export class PermissionContext {
  constructor(options = {}) {
    this.mode = options.mode || 'default';
    this.cwd = options.cwd || process.cwd();
    this.additionalWorkingDirectories = new Map(options.additionalWorkingDirectories || []);
    this.alwaysAllowRules = options.alwaysAllowRules || {};
    this.alwaysDenyRules = options.alwaysDenyRules || {};
    this.isBypassPermissionsModeAvailable = options.isBypassPermissionsModeAvailable || false;
  }

  /**
   * 创建默认权限上下文
   * @original: vV()函数 (L8343)
   */
  static createDefault() {
    return new PermissionContext({
      mode: 'default',
      cwd: process.cwd(),
      additionalWorkingDirectories: new Map(),
      alwaysAllowRules: {},
      alwaysDenyRules: {},
      isBypassPermissionsModeAvailable: false
    });
  }

  /**
   * 更新权限上下文
   * @param {Object} updates - 要更新的属性
   * @returns {PermissionContext} 新的权限上下文实例
   */
  update(updates) {
    return new PermissionContext({
      mode: updates.mode ?? this.mode,
      cwd: updates.cwd ?? this.cwd,
      additionalWorkingDirectories: updates.additionalWorkingDirectories ?? this.additionalWorkingDirectories,
      alwaysAllowRules: updates.alwaysAllowRules ?? this.alwaysAllowRules,
      alwaysDenyRules: updates.alwaysDenyRules ?? this.alwaysDenyRules,
      isBypassPermissionsModeAvailable: updates.isBypassPermissionsModeAvailable ?? this.isBypassPermissionsModeAvailable
    });
  }

  /**
   * 检查是否为绕过权限模式
   */
  isBypassMode() {
    return this.mode === 'bypassPermissions';
  }

  /**
   * 检查是否为接受编辑模式
   */
  isAcceptEditsMode() {
    return this.mode === 'acceptEdits';
  }

  /**
   * 检查是否为计划模式
   */
  isPlanMode() {
    return this.mode === 'plan';
  }
}

/**
 * 权限规则类
 * 表示单个权限规则
 */
export class PermissionRule {
  constructor(toolName, specifier = '', behavior = PERMISSION_BEHAVIORS.ASK, source = 'user') {
    this.toolName = toolName;
    this.specifier = specifier;
    this.behavior = behavior;
    this.source = source;
    this.ruleValue = specifier ? `${toolName}(${specifier})` : toolName;
  }

  /**
   * 检查规则是否匹配给定的工具和参数
   * @param {string} toolName - 工具名称
   * @param {string} specifier - 规则说明符
   * @returns {boolean} 是否匹配
   */
  matches(toolName, specifier = '') {
    if (this.toolName !== toolName) return false;
    if (!this.specifier) return true; // 通用规则匹配所有
    return this.specifier === specifier;
  }

  /**
   * 将规则转换为字符串表示
   */
  toString() {
    return this.ruleValue;
  }
}

/**
 * 权限决策结果类
 */
export class PermissionDecision {
  constructor(behavior, options = {}) {
    this.behavior = behavior;
    this.message = options.message;
    this.updatedInput = options.updatedInput;
    this.decisionReason = options.decisionReason;
    this.ruleSuggestions = options.ruleSuggestions;
  }

  /**
   * 创建允许决策
   */
  static allow(updatedInput, decisionReason) {
    return new PermissionDecision(PERMISSION_BEHAVIORS.ALLOW, {
      updatedInput,
      decisionReason
    });
  }

  /**
   * 创建拒绝决策
   */
  static deny(message, decisionReason, ruleSuggestions = null) {
    return new PermissionDecision(PERMISSION_BEHAVIORS.DENY, {
      message,
      decisionReason,
      ruleSuggestions
    });
  }

  /**
   * 创建询问决策
   */
  static ask(message, decisionReason, ruleSuggestions = null) {
    return new PermissionDecision(PERMISSION_BEHAVIORS.ASK, {
      message,
      decisionReason,
      ruleSuggestions
    });
  }

  /**
   * 创建透传决策
   */
  static passthrough(message, ruleSuggestions = null) {
    return new PermissionDecision(PERMISSION_BEHAVIORS.PASSTHROUGH, {
      message,
      ruleSuggestions
    });
  }
}

/**
 * 权限管理器类
 * 核心权限决策引擎
 */
export class PermissionManager {
  constructor() {
    this.rules = new Map(); // toolName -> PermissionRule[]
  }

  /**
   * 添加权限规则
   * @param {PermissionRule} rule - 权限规则
   */
  addRule(rule) {
    if (!this.rules.has(rule.toolName)) {
      this.rules.set(rule.toolName, []);
    }
    this.rules.get(rule.toolName).push(rule);
  }

  /**
   * 移除权限规则
   * @param {string} toolName - 工具名称
   * @param {string} specifier - 规则说明符
   */
  removeRule(toolName, specifier = '') {
    const rules = this.rules.get(toolName);
    if (!rules) return false;

    const index = rules.findIndex(rule => rule.matches(toolName, specifier));
    if (index >= 0) {
      rules.splice(index, 1);
      if (rules.length === 0) {
        this.rules.delete(toolName);
      }
      return true;
    }
    return false;
  }

  /**
   * 查找匹配的规则
   * @param {string} toolName - 工具名称
   * @param {string} specifier - 规则说明符
   * @returns {PermissionRule|null} 匹配的规则
   */
  findMatchingRule(toolName, specifier = '') {
    const rules = this.rules.get(toolName);
    if (!rules) return null;

    // 优先匹配具体规则，然后匹配通用规则
    const specificRule = rules.find(rule => rule.specifier && rule.matches(toolName, specifier));
    if (specificRule) return specificRule;

    const generalRule = rules.find(rule => !rule.specifier);
    return generalRule || null;
  }

  /**
   * 检查工具权限
   * @original: Nw()函数 (L3337)
   * @param {Object} tool - 工具对象
   * @param {Object} input - 工具输入
   * @param {PermissionContext} context - 权限上下文
   * @returns {Promise<PermissionDecision>} 权限决策
   */
  async checkToolPermissions(tool, input, context) {
    try {
      // 检查中止信号
      if (context.abortController?.signal.aborted) {
        throw new Error('Operation aborted');
      }

      // 检查拒绝规则
      const denyRule = this.findMatchingRule(tool.name, 'deny');
      if (denyRule) {
        return PermissionDecision.deny(
          `Permission to use ${tool.name} has been denied.`,
          {
            type: DECISION_REASON_TYPES.RULE,
            rule: denyRule
          }
        );
      }

      // 绕过权限模式
      if (context.isBypassMode()) {
        return PermissionDecision.allow(input, {
          type: DECISION_REASON_TYPES.MODE,
          mode: context.mode
        });
      }

      // 检查允许规则
      const allowRule = this.findMatchingRule(tool.name, 'allow');
      if (allowRule) {
        return PermissionDecision.allow(input, {
          type: DECISION_REASON_TYPES.RULE,
          rule: allowRule
        });
      }

      // 调用工具的权限检查方法
      let toolDecision;
      try {
        const parsedInput = tool.inputSchema.parse(input);
        toolDecision = await tool.checkPermissions(parsedInput, context);
      } catch (error) {
        logger.error('权限检查错误:', error);
        return PermissionDecision.ask('Error checking permissions');
      }

      if (toolDecision?.behavior === PERMISSION_BEHAVIORS.DENY) {
        return toolDecision;
      }

      // 默认返回询问决策
      return toolDecision?.behavior === PERMISSION_BEHAVIORS.PASSTHROUGH 
        ? PermissionDecision.ask(
            `Claude requested permissions to use ${tool.name}, but you haven't granted it yet.`
          )
        : toolDecision;

    } catch (error) {
      logger.error('权限检查失败:', error);
      return PermissionDecision.ask('Error checking permissions');
    }
  }

  /**
   * 获取所有规则
   * @returns {PermissionRule[]} 所有权限规则
   */
  getAllRules() {
    const allRules = [];
    for (const rules of this.rules.values()) {
      allRules.push(...rules);
    }
    return allRules;
  }

  /**
   * 清空所有规则
   */
  clearAllRules() {
    this.rules.clear();
  }
}

/**
 * 全局权限管理器实例
 */
export const globalPermissionManager = new PermissionManager();

/**
 * 创建默认权限上下文
 * @original: vV()函数
 */
export function createDefaultPermissionContext() {
  return PermissionContext.createDefault();
}
