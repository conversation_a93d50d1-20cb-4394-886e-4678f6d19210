/**
 * 应用状态管理
 * 
 * 管理Claude Code的全局应用状态
 * 
 * @original: 应用状态相关代码 (L8389-8423, L53681-53714)
 */

import React from 'react';
import { logger } from '../utils/logger.js';

/**
 * 应用状态上下文
 */
const AppStateContext = React.createContext(null);
const AppStateProviderContext = React.createContext(false);

/**
 * 默认应用状态
 * @original: Pr4()函数 (L8360)
 */
export function createDefaultAppState() {
  return {
    // 会话状态
    sessionId: null,
    isInitialized: false,
    
    // UI状态
    mode: 'responding', // 'responding' | 'waiting' | 'error'
    currentView: 'main', // 'main' | 'transcript' | 'settings'
    
    // 消息和对话
    messages: [],
    streamingMessages: [],
    toolUseConfirmQueue: [],
    inProgressToolUseIDs: new Set(),
    
    // 输入状态
    inputValue: '',
    inputMode: 'prompt', // 'prompt' | 'command'
    isMessageSelectorVisible: false,
    
    // 工具和权限
    toolJSX: null,
    toolPermissionContext: null,
    
    // 配置和设置
    dynamicMcpConfig: {},
    queuedCommands: [],
    
    // 状态标志
    isLoading: false,
    hasError: false,
    errorMessage: null,
    
    // 成本和使用情况
    apiUsage: {
      inputTokens: 0,
      outputTokens: 0,
      cost: 0
    },
    hasAcknowledgedCostThreshold: false,
    
    // 通知和提示
    notifications: [],
    showCostWarning: false,
    showInstallationDialog: false,
    
    // 终端和焦点
    terminalFocus: true,
    isTypingWithoutFocus: false,
    
    // 其他状态
    autoUpdaterResult: null,
    installationStatus: null,
    showBashesDialog: false
  };
}

/**
 * 应用状态Provider组件
 * @original: AppStateProvider组件 (L8389-8423)
 * @param {Object} props - 组件属性
 * @returns {JSX.Element} Provider组件
 */
export function AppStateProvider({
  children,
  initialState,
  onStateChange
}) {
  // 检查嵌套Provider
  const isNested = React.useContext(AppStateProviderContext);
  if (isNested) {
    throw new Error('AppStateProvider cannot be nested within another AppStateProvider');
  }

  // 状态管理
  const [stateContainer, setStateContainer] = React.useState({
    currentState: initialState || createDefaultAppState(),
    previousState: null
  });

  // 状态更新函数
  const updateState = React.useCallback((updates) => {
    setStateContainer(prev => {
      const newState = typeof updates === 'function' 
        ? updates(prev.currentState)
        : { ...prev.currentState, ...updates };

      // 标记为已初始化
      newState.__IS_INITIALIZED__ = true;

      return {
        currentState: newState,
        previousState: prev.currentState
      };
    });
  }, []);

  // 获取状态的记忆化版本
  const memoizedState = React.useMemo(() => {
    const state = stateContainer.currentState;
    
    // 确保状态已初始化
    if (!state.__IS_INITIALIZED__) {
      state.__IS_INITIALIZED__ = true;
    }
    
    return state;
  }, [stateContainer.currentState]);

  // 状态变化回调
  React.useEffect(() => {
    onStateChange?.({
      newState: stateContainer.currentState,
      oldState: stateContainer.previousState
    });
  }, [stateContainer.currentState, stateContainer.previousState, onStateChange]);

  // 提供状态上下文
  const contextValue = React.useMemo(() => ({
    state: memoizedState,
    updateState,
    previousState: stateContainer.previousState
  }), [memoizedState, updateState, stateContainer.previousState]);

  return React.createElement(AppStateProviderContext.Provider, {
    value: true
  },
    React.createElement(AppStateContext.Provider, {
      value: contextValue
    }, children)
  );
}

/**
 * 使用应用状态的Hook
 * @returns {Object} 应用状态和更新函数
 */
export function useAppState() {
  const context = React.useContext(AppStateContext);
  
  if (!context) {
    throw new Error('useAppState must be used within an AppStateProvider');
  }
  
  return context;
}

/**
 * 状态选择器Hook
 * @param {Function} selector - 状态选择函数
 * @returns {*} 选择的状态值
 */
export function useAppStateSelector(selector) {
  const { state } = useAppState();
  
  return React.useMemo(() => {
    return selector(state);
  }, [state, selector]);
}

/**
 * 状态更新Hook
 * @returns {Function} 状态更新函数
 */
export function useAppStateUpdater() {
  const { updateState } = useAppState();
  return updateState;
}

/**
 * 消息状态Hook
 * @returns {Object} 消息相关状态和操作
 */
export function useMessages() {
  const { state, updateState } = useAppState();
  
  const addMessage = React.useCallback((message) => {
    updateState(prevState => ({
      ...prevState,
      messages: [...prevState.messages, message]
    }));
  }, [updateState]);

  const updateMessage = React.useCallback((messageId, updates) => {
    updateState(prevState => ({
      ...prevState,
      messages: prevState.messages.map(msg => 
        msg.id === messageId ? { ...msg, ...updates } : msg
      )
    }));
  }, [updateState]);

  const removeMessage = React.useCallback((messageId) => {
    updateState(prevState => ({
      ...prevState,
      messages: prevState.messages.filter(msg => msg.id !== messageId)
    }));
  }, [updateState]);

  const clearMessages = React.useCallback(() => {
    updateState(prevState => ({
      ...prevState,
      messages: []
    }));
  }, [updateState]);

  return {
    messages: state.messages,
    streamingMessages: state.streamingMessages,
    addMessage,
    updateMessage,
    removeMessage,
    clearMessages
  };
}

/**
 * 工具状态Hook
 * @returns {Object} 工具相关状态和操作
 */
export function useTools() {
  const { state, updateState } = useAppState();

  const setToolJSX = React.useCallback((jsx) => {
    updateState(prevState => ({
      ...prevState,
      toolJSX: jsx
    }));
  }, [updateState]);

  const addToToolUseQueue = React.useCallback((toolUse) => {
    updateState(prevState => ({
      ...prevState,
      toolUseConfirmQueue: [...prevState.toolUseConfirmQueue, toolUse]
    }));
  }, [updateState]);

  const removeFromToolUseQueue = React.useCallback((toolUseId) => {
    updateState(prevState => ({
      ...prevState,
      toolUseConfirmQueue: prevState.toolUseConfirmQueue.filter(
        item => item.id !== toolUseId
      )
    }));
  }, [updateState]);

  const addInProgressToolUse = React.useCallback((toolUseId) => {
    updateState(prevState => ({
      ...prevState,
      inProgressToolUseIDs: new Set([...prevState.inProgressToolUseIDs, toolUseId])
    }));
  }, [updateState]);

  const removeInProgressToolUse = React.useCallback((toolUseId) => {
    updateState(prevState => {
      const newSet = new Set(prevState.inProgressToolUseIDs);
      newSet.delete(toolUseId);
      return {
        ...prevState,
        inProgressToolUseIDs: newSet
      };
    });
  }, [updateState]);

  return {
    toolJSX: state.toolJSX,
    toolUseConfirmQueue: state.toolUseConfirmQueue,
    inProgressToolUseIDs: state.inProgressToolUseIDs,
    toolPermissionContext: state.toolPermissionContext,
    setToolJSX,
    addToToolUseQueue,
    removeFromToolUseQueue,
    addInProgressToolUse,
    removeInProgressToolUse
  };
}

/**
 * 通知状态Hook
 * @returns {Object} 通知相关状态和操作
 */
export function useNotifications() {
  const { state, updateState } = useAppState();

  const addNotification = React.useCallback((notification) => {
    const id = Date.now().toString();
    const notificationWithId = { id, ...notification };
    
    updateState(prevState => ({
      ...prevState,
      notifications: [...prevState.notifications, notificationWithId]
    }));

    // 自动移除通知
    if (notification.autoRemove !== false) {
      setTimeout(() => {
        removeNotification(id);
      }, notification.duration || 5000);
    }

    return id;
  }, [updateState]);

  const removeNotification = React.useCallback((notificationId) => {
    updateState(prevState => ({
      ...prevState,
      notifications: prevState.notifications.filter(
        notification => notification.id !== notificationId
      )
    }));
  }, [updateState]);

  const clearNotifications = React.useCallback(() => {
    updateState(prevState => ({
      ...prevState,
      notifications: []
    }));
  }, [updateState]);

  return {
    notifications: state.notifications,
    addNotification,
    removeNotification,
    clearNotifications
  };
}

/**
 * API使用情况Hook
 * @returns {Object} API使用情况相关状态和操作
 */
export function useApiUsage() {
  const { state, updateState } = useAppState();

  const updateApiUsage = React.useCallback((usage) => {
    updateState(prevState => ({
      ...prevState,
      apiUsage: {
        ...prevState.apiUsage,
        ...usage
      }
    }));
  }, [updateState]);

  const resetApiUsage = React.useCallback(() => {
    updateState(prevState => ({
      ...prevState,
      apiUsage: {
        inputTokens: 0,
        outputTokens: 0,
        cost: 0
      }
    }));
  }, [updateState]);

  return {
    apiUsage: state.apiUsage,
    updateApiUsage,
    resetApiUsage
  };
}

/**
 * 状态持久化Hook
 * @param {string} key - 存储键
 * @param {*} defaultValue - 默认值
 * @returns {Array} [value, setValue]
 */
export function usePersistedState(key, defaultValue) {
  const [value, setValue] = React.useState(() => {
    try {
      const item = localStorage.getItem(key);
      return item ? JSON.parse(item) : defaultValue;
    } catch (error) {
      logger.warn('读取持久化状态失败', { key, error: error.message });
      return defaultValue;
    }
  });

  const setPersistedValue = React.useCallback((newValue) => {
    try {
      setValue(newValue);
      localStorage.setItem(key, JSON.stringify(newValue));
    } catch (error) {
      logger.error('保存持久化状态失败', { key, error: error.message });
    }
  }, [key]);

  return [value, setPersistedValue];
}
