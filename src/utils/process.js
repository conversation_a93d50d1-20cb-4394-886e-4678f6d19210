/**
 * 进程执行工具
 * 
 * 提供安全的进程执行、命令运行、超时控制等功能
 * 
 * @original: 行16625-17500的进程执行相关代码
 */

import { spawn, execFile, execSync } from 'child_process';
import { promisify } from 'util';
import { logger } from './logger.js';

const execFileAsync = promisify(execFile);

/**
 * 命令执行结果接口
 */
export class CommandResult {
  constructor(options = {}) {
    this.code = options.code || 0;
    this.stdout = options.stdout || '';
    this.stderr = options.stderr || '';
    this.interrupted = options.interrupted || false;
    this.backgroundTaskId = options.backgroundTaskId || null;
    this.duration = options.duration || 0;
  }

  /**
   * 检查命令是否成功执行
   */
  isSuccess() {
    return this.code === 0 && !this.interrupted;
  }

  /**
   * 获取组合输出
   */
  getCombinedOutput() {
    return this.stdout + (this.stderr ? '\n' + this.stderr : '');
  }
}

/**
 * 执行选项接口
 */
export class ExecutionOptions {
  constructor(options = {}) {
    this.timeout = options.timeout || 30000; // 30秒默认超时
    this.cwd = options.cwd || process.cwd();
    this.env = options.env || process.env;
    this.shell = options.shell || false;
    this.maxBuffer = options.maxBuffer || 1024 * 1024 * 10; // 10MB
    this.encoding = options.encoding || 'utf8';
    this.preserveOutputOnError = options.preserveOutputOnError !== false;
    this.abortSignal = options.abortSignal || null;
  }
}

/**
 * 执行命令（异步）
 * @original: X2()函数的核心逻辑
 * @param {string} command - 命令
 * @param {string[]} args - 参数数组
 * @param {ExecutionOptions} options - 执行选项
 * @returns {Promise<CommandResult>} 执行结果
 */
export async function executeCommand(command, args = [], options = {}) {
  const execOptions = new ExecutionOptions(options);
  const startTime = Date.now();

  logger.debug('执行命令', { command, args, options: execOptions });

  try {
    const { stdout, stderr } = await execFileAsync(command, args, {
      cwd: execOptions.cwd,
      env: execOptions.env,
      timeout: execOptions.timeout,
      maxBuffer: execOptions.maxBuffer,
      encoding: execOptions.encoding,
      signal: execOptions.abortSignal
    });

    const duration = Date.now() - startTime;
    logger.debug('命令执行完成', { command, duration, code: 0 });

    return new CommandResult({
      code: 0,
      stdout: stdout || '',
      stderr: stderr || '',
      duration
    });

  } catch (error) {
    const duration = Date.now() - startTime;
    
    if (error.signal === 'SIGTERM' || error.signal === 'SIGKILL') {
      logger.warn('命令被中止', { command, signal: error.signal });
      return new CommandResult({
        code: 145,
        stdout: '',
        stderr: 'Command aborted before execution',
        interrupted: true,
        duration
      });
    }

    const code = error.code || 1;
    const stdout = execOptions.preserveOutputOnError ? (error.stdout || '') : '';
    const stderr = execOptions.preserveOutputOnError ? (error.stderr || error.message) : error.message;

    logger.error('命令执行失败', { 
      command, 
      code, 
      error: error.message,
      duration 
    });

    return new CommandResult({
      code,
      stdout,
      stderr,
      duration
    });
  }
}

/**
 * 执行命令（同步）
 * @original: execSync相关代码
 * @param {string} command - 命令
 * @param {ExecutionOptions} options - 执行选项
 * @returns {CommandResult} 执行结果
 */
export function executeCommandSync(command, options = {}) {
  const execOptions = new ExecutionOptions(options);
  const startTime = Date.now();

  logger.debug('同步执行命令', { command, options: execOptions });

  try {
    const stdout = execSync(command, {
      cwd: execOptions.cwd,
      env: execOptions.env,
      timeout: execOptions.timeout,
      maxBuffer: execOptions.maxBuffer,
      encoding: execOptions.encoding
    });

    const duration = Date.now() - startTime;
    logger.debug('同步命令执行完成', { command, duration });

    return new CommandResult({
      code: 0,
      stdout: stdout || '',
      stderr: '',
      duration
    });

  } catch (error) {
    const duration = Date.now() - startTime;
    const code = error.status || 1;
    const stdout = execOptions.preserveOutputOnError ? (error.stdout?.toString() || '') : '';
    const stderr = execOptions.preserveOutputOnError ? (error.stderr?.toString() || error.message) : error.message;

    logger.error('同步命令执行失败', { 
      command, 
      code, 
      error: error.message,
      duration 
    });

    return new CommandResult({
      code,
      stdout,
      stderr,
      duration
    });
  }
}

/**
 * 生成进程（用于长时间运行的命令）
 * @original: spawn相关代码
 * @param {string} command - 命令
 * @param {string[]} args - 参数数组
 * @param {ExecutionOptions} options - 执行选项
 * @returns {ChildProcess} 子进程对象
 */
export function spawnProcess(command, args = [], options = {}) {
  const execOptions = new ExecutionOptions(options);

  logger.debug('生成进程', { command, args, options: execOptions });

  const childProcess = spawn(command, args, {
    cwd: execOptions.cwd,
    env: execOptions.env,
    shell: execOptions.shell,
    stdio: ['pipe', 'pipe', 'pipe']
  });

  // 设置超时
  if (execOptions.timeout > 0) {
    const timeoutId = setTimeout(() => {
      logger.warn('进程超时，正在终止', { command, timeout: execOptions.timeout });
      childProcess.kill('SIGTERM');
      
      // 如果SIGTERM不起作用，5秒后使用SIGKILL
      setTimeout(() => {
        if (!childProcess.killed) {
          logger.warn('强制终止进程', { command });
          childProcess.kill('SIGKILL');
        }
      }, 5000);
    }, execOptions.timeout);

    childProcess.on('exit', () => {
      clearTimeout(timeoutId);
    });
  }

  // 处理中止信号
  if (execOptions.abortSignal) {
    const abortHandler = () => {
      logger.debug('收到中止信号，终止进程', { command });
      childProcess.kill('SIGTERM');
    };

    execOptions.abortSignal.addEventListener('abort', abortHandler);
    
    childProcess.on('exit', () => {
      execOptions.abortSignal.removeEventListener('abort', abortHandler);
    });
  }

  return childProcess;
}

/**
 * 检查命令是否存在
 * @param {string} command - 命令名称
 * @returns {Promise<boolean>} 命令是否存在
 */
export async function commandExists(command) {
  try {
    const whichCommand = process.platform === 'win32' ? 'where' : 'which';
    const result = await executeCommand(whichCommand, [command]);
    return result.isSuccess();
  } catch (error) {
    logger.debug('检查命令存在性失败', { command, error: error.message });
    return false;
  }
}

/**
 * 获取命令的完整路径
 * @param {string} command - 命令名称
 * @returns {Promise<string|null>} 命令的完整路径，如果不存在则返回null
 */
export async function getCommandPath(command) {
  try {
    const whichCommand = process.platform === 'win32' ? 'where' : 'which';
    const result = await executeCommand(whichCommand, [command]);
    
    if (result.isSuccess()) {
      return result.stdout.trim().split('\n')[0]; // 返回第一个匹配的路径
    }
    
    return null;
  } catch (error) {
    logger.debug('获取命令路径失败', { command, error: error.message });
    return null;
  }
}

/**
 * 安全地引用命令参数
 * @original: shell引用相关代码
 * @param {string} arg - 参数
 * @returns {string} 引用后的参数
 */
export function quoteArgument(arg) {
  if (typeof arg !== 'string') {
    arg = String(arg);
  }

  // 如果参数不包含特殊字符，直接返回
  if (!/[\s"'`$\\|&;<>(){}[\]*?~]/.test(arg)) {
    return arg;
  }

  // 在Windows上使用双引号
  if (process.platform === 'win32') {
    return `"${arg.replace(/"/g, '""')}"`;
  }

  // 在Unix系统上使用单引号
  return `'${arg.replace(/'/g, "'\"'\"'")}'`;
}

/**
 * 构建安全的命令行
 * @param {string} command - 命令
 * @param {string[]} args - 参数数组
 * @returns {string} 安全的命令行字符串
 */
export function buildSafeCommandLine(command, args = []) {
  const quotedCommand = quoteArgument(command);
  const quotedArgs = args.map(arg => quoteArgument(arg));
  return [quotedCommand, ...quotedArgs].join(' ');
}

/**
 * 解析命令行字符串为命令和参数
 * @param {string} commandLine - 命令行字符串
 * @returns {Object} 包含command和args的对象
 */
export function parseCommandLine(commandLine) {
  const parts = [];
  let current = '';
  let inQuotes = false;
  let quoteChar = '';

  for (let i = 0; i < commandLine.length; i++) {
    const char = commandLine[i];

    if (!inQuotes && (char === '"' || char === "'")) {
      inQuotes = true;
      quoteChar = char;
    } else if (inQuotes && char === quoteChar) {
      inQuotes = false;
      quoteChar = '';
    } else if (!inQuotes && /\s/.test(char)) {
      if (current) {
        parts.push(current);
        current = '';
      }
    } else {
      current += char;
    }
  }

  if (current) {
    parts.push(current);
  }

  return {
    command: parts[0] || '',
    args: parts.slice(1)
  };
}

/**
 * 创建带有超时的AbortController
 * @param {number} timeout - 超时时间（毫秒）
 * @returns {AbortController} AbortController实例
 */
export function createTimeoutController(timeout) {
  const controller = new AbortController();
  
  if (timeout > 0) {
    setTimeout(() => {
      controller.abort();
    }, timeout);
  }
  
  return controller;
}
