/**
 * 环境设置和配置工具
 * 
 * 负责设置运行环境、处理环境变量、跨平台兼容性等
 * 
 * @original: 行2365-2396的环境配置代码
 */

import { platform } from 'os';
import { logger } from './logger.js';

/**
 * 模拟浏览器环境对象
 * @original: j_9, y_9, k_9对象 (L2336-2364)
 */
const mockDocument = {
  visibilityState: "visible",
  documentElement: {
    lang: "en"
  },
  addEventListener: (event, handler) => {
    // 空实现，用于兼容性
  }
};

const mockWindow = {
  document: mockDocument,
  location: {
    href: "node://localhost",
    pathname: "/"
  },
  addEventListener: (event, handler) => {
    if (event === "beforeunload") {
      process.on("exit", () => {
        if (typeof handler === "function") {
          handler({});
        } else {
          handler.handleEvent({});
        }
      });
    }
  },
  focus: () => {},
  innerHeight: 768,
  innerWidth: 1024
};

const mockNavigator = {
  sendBeacon: (url, data) => {
    return true;
  },
  userAgent: "Mozilla/5.0 (Node.js) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0",
  language: "en-US"
};

/**
 * 设置全局环境
 * 为Node.js环境提供浏览器API兼容性
 * 
 * @original: 行2365-2366的全局对象设置
 */
export function setupGlobalEnvironment() {
  // 设置浏览器兼容性全局对象
  if (typeof window === "undefined") {
    global.window = mockWindow;
  }
  
  if (typeof navigator === "undefined") {
    global.navigator = mockNavigator;
  }
  
  logger.debug('全局环境已设置');
}

/**
 * 解析布尔值环境变量
 * @original: Uo0()函数 (L2367)
 * @param {string} value - 环境变量值
 * @returns {boolean} 解析后的布尔值
 */
export function parseBooleanEnvVar(value) {
  if (!value) return false;
  
  const normalizedValue = value.toLowerCase().trim();
  return ["0", "false", "no", "off"].includes(normalizedValue);
}

/**
 * 解析环境变量数组
 * @original: wo0()函数 (L2372)
 * @param {string[]} envArray - 环境变量数组，格式为 ["KEY1=value1", "KEY2=value2"]
 * @returns {Object} 解析后的环境变量对象
 * @throws {Error} 格式无效时抛出错误
 */
export function parseEnvironmentVariables(envArray) {
  const result = {};
  
  if (envArray) {
    for (const envVar of envArray) {
      const [key, ...valueParts] = envVar.split("=");
      
      if (!key || valueParts.length === 0) {
        throw new Error(
          `Invalid environment variable format: ${envVar}, ` +
          `environment variables should be added as: -e KEY1=value1 -e KEY2=value2`
        );
      }
      
      result[key] = valueParts.join("=");
    }
  }
  
  return result;
}

/**
 * 获取云ML区域
 * @original: $O()函数 (L2381)
 * @returns {string} 云ML区域
 */
export function getCloudMLRegion() {
  return process.env.CLOUD_ML_REGION || "us-east5";
}

/**
 * 检查是否维护项目工作目录
 * @original: Ni1()函数 (L2384)
 * @returns {boolean} 是否维护项目工作目录
 */
export function shouldMaintainProjectWorkingDir() {
  return parseBooleanEnvVar(process.env.CLAUDE_BASH_MAINTAIN_PROJECT_WORKING_DIR);
}

/**
 * 根据模型名称获取Vertex区域
 * @original: $o0()函数 (L2387)
 * @param {string} modelName - 模型名称
 * @returns {string} Vertex区域
 */
export function getVertexRegionForModel(modelName) {
  if (modelName?.startsWith("claude-3-5-haiku")) {
    return process.env.VERTEX_REGION_CLAUDE_3_5_HAIKU || getCloudMLRegion();
  }
  
  if (modelName?.startsWith("claude-3-5-sonnet")) {
    return process.env.VERTEX_REGION_CLAUDE_3_5_SONNET || getCloudMLRegion();
  }
  
  if (modelName?.startsWith("claude-3-7-sonnet")) {
    return process.env.VERTEX_REGION_CLAUDE_3_7_SONNET || getCloudMLRegion();
  }
  
  if (modelName?.startsWith("claude-opus-4-1")) {
    return process.env.VERTEX_REGION_CLAUDE_4_1_OPUS || getCloudMLRegion();
  }
  
  if (modelName?.startsWith("claude-opus-4")) {
    return process.env.VERTEX_REGION_CLAUDE_4_0_OPUS || getCloudMLRegion();
  }
  
  if (modelName?.startsWith("claude-sonnet-4")) {
    return process.env.VERTEX_REGION_CLAUDE_4_0_SONNET || getCloudMLRegion();
  }
  
  return getCloudMLRegion();
}

/**
 * 获取当前平台信息
 * @returns {Object} 平台信息
 */
export function getPlatformInfo() {
  const currentPlatform = platform();
  
  return {
    platform: currentPlatform,
    isWindows: currentPlatform === 'win32',
    isMacOS: currentPlatform === 'darwin',
    isLinux: currentPlatform === 'linux',
    arch: process.arch,
    nodeVersion: process.version
  };
}

/**
 * 检查是否在CI环境中运行
 * @returns {boolean} 是否在CI环境中
 */
export function isRunningInCI() {
  return !!(
    process.env.CI ||
    process.env.CONTINUOUS_INTEGRATION ||
    process.env.BUILD_NUMBER ||
    process.env.GITHUB_ACTIONS ||
    process.env.TRAVIS ||
    process.env.CIRCLECI ||
    process.env.JENKINS_URL
  );
}

/**
 * 获取环境类型
 * @returns {string} 环境类型：'development', 'production', 'test', 'ci'
 */
export function getEnvironmentType() {
  if (isRunningInCI()) return 'ci';
  if (process.env.NODE_ENV === 'test') return 'test';
  if (process.env.NODE_ENV === 'production') return 'production';
  return 'development';
}

/**
 * 设置环境变量默认值
 * @param {Object} defaults - 默认环境变量
 */
export function setEnvironmentDefaults(defaults) {
  for (const [key, value] of Object.entries(defaults)) {
    if (!(key in process.env)) {
      process.env[key] = value;
      logger.debug(`设置默认环境变量: ${key}=${value}`);
    }
  }
}

/**
 * 验证必需的环境变量
 * @param {string[]} requiredVars - 必需的环境变量名称
 * @throws {Error} 缺少必需环境变量时抛出错误
 */
export function validateRequiredEnvironmentVariables(requiredVars) {
  const missing = requiredVars.filter(varName => !(varName in process.env));
  
  if (missing.length > 0) {
    throw new Error(`缺少必需的环境变量: ${missing.join(', ')}`);
  }
}

/**
 * 主环境设置函数
 * 执行所有必要的环境初始化
 */
export function setupEnvironment() {
  logger.debug('开始环境设置');
  
  // 设置全局浏览器兼容性对象
  setupGlobalEnvironment();
  
  // 设置默认环境变量
  setEnvironmentDefaults({
    NODE_ENV: 'development',
    CLAUDE_LOG_LEVEL: 'info'
  });
  
  // 记录平台信息
  const platformInfo = getPlatformInfo();
  logger.debug('平台信息', platformInfo);
  
  // 记录环境类型
  const envType = getEnvironmentType();
  logger.debug(`环境类型: ${envType}`);
  
  // 在CI环境中发出警告
  if (isRunningInCI()) {
    logger.warn('在CI环境中运行 - 交互功能受限');
  }
  
  logger.debug('环境设置完成');
}
