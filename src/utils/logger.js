/**
 * 日志记录工具
 * 
 * 提供统一的日志记录接口，支持不同级别的日志输出
 */

/**
 * 日志级别枚举
 */
export const LogLevel = {
  ERROR: 0,
  WARN: 1,
  INFO: 2,
  DEBUG: 3,
  TRACE: 4
};

/**
 * 日志级别名称映射
 */
const LOG_LEVEL_NAMES = {
  [LogLevel.ERROR]: 'ERROR',
  [LogLevel.WARN]: 'WARN',
  [LogLevel.INFO]: 'INFO',
  [LogLevel.DEBUG]: 'DEBUG',
  [LogLevel.TRACE]: 'TRACE'
};

/**
 * 颜色代码（用于终端输出）
 */
const COLORS = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  green: '\x1b[32m',
  gray: '\x1b[90m'
};

/**
 * 日志级别颜色映射
 */
const LEVEL_COLORS = {
  [LogLevel.ERROR]: COLORS.red,
  [LogLevel.WARN]: COLORS.yellow,
  [LogLevel.INFO]: COLORS.blue,
  [LogLevel.DEBUG]: COLORS.green,
  [LogLevel.TRACE]: COLORS.gray
};

/**
 * Logger类
 * 提供结构化的日志记录功能
 */
class Logger {
  constructor(options = {}) {
    this.level = this._parseLogLevel(options.level || process.env.CLAUDE_LOG_LEVEL || 'info');
    this.enableColors = options.enableColors !== false && process.stdout.isTTY;
    this.prefix = options.prefix || '';
  }

  /**
   * 解析日志级别
   * @param {string|number} level - 日志级别
   * @returns {number} 数字形式的日志级别
   * @private
   */
  _parseLogLevel(level) {
    if (typeof level === 'number') {
      return level;
    }

    const levelStr = level.toUpperCase();
    switch (levelStr) {
      case 'ERROR': return LogLevel.ERROR;
      case 'WARN': return LogLevel.WARN;
      case 'INFO': return LogLevel.INFO;
      case 'DEBUG': return LogLevel.DEBUG;
      case 'TRACE': return LogLevel.TRACE;
      default: return LogLevel.INFO;
    }
  }

  /**
   * 格式化日志消息
   * @param {number} level - 日志级别
   * @param {string} message - 消息
   * @param {*} data - 附加数据
   * @returns {string} 格式化后的消息
   * @private
   */
  _formatMessage(level, message, data) {
    const timestamp = new Date().toISOString();
    const levelName = LOG_LEVEL_NAMES[level];
    const color = this.enableColors ? LEVEL_COLORS[level] : '';
    const reset = this.enableColors ? COLORS.reset : '';
    
    let formattedMessage = `${color}[${timestamp}] ${levelName}${reset}`;
    
    if (this.prefix) {
      formattedMessage += ` [${this.prefix}]`;
    }
    
    formattedMessage += `: ${message}`;
    
    if (data !== undefined) {
      if (typeof data === 'object') {
        formattedMessage += '\n' + JSON.stringify(data, null, 2);
      } else {
        formattedMessage += ` ${data}`;
      }
    }
    
    return formattedMessage;
  }

  /**
   * 记录日志
   * @param {number} level - 日志级别
   * @param {string} message - 消息
   * @param {*} data - 附加数据
   * @private
   */
  _log(level, message, data) {
    if (level <= this.level) {
      const formattedMessage = this._formatMessage(level, message, data);
      
      if (level <= LogLevel.WARN) {
        console.error(formattedMessage);
      } else {
        console.log(formattedMessage);
      }
    }
  }

  /**
   * 记录错误日志
   * @param {string} message - 消息
   * @param {*} data - 附加数据
   */
  error(message, data) {
    this._log(LogLevel.ERROR, message, data);
  }

  /**
   * 记录警告日志
   * @param {string} message - 消息
   * @param {*} data - 附加数据
   */
  warn(message, data) {
    this._log(LogLevel.WARN, message, data);
  }

  /**
   * 记录信息日志
   * @param {string} message - 消息
   * @param {*} data - 附加数据
   */
  info(message, data) {
    this._log(LogLevel.INFO, message, data);
  }

  /**
   * 记录调试日志
   * @param {string} message - 消息
   * @param {*} data - 附加数据
   */
  debug(message, data) {
    this._log(LogLevel.DEBUG, message, data);
  }

  /**
   * 记录跟踪日志
   * @param {string} message - 消息
   * @param {*} data - 附加数据
   */
  trace(message, data) {
    this._log(LogLevel.TRACE, message, data);
  }

  /**
   * 设置日志级别
   * @param {string|number} level - 新的日志级别
   */
  setLevel(level) {
    this.level = this._parseLogLevel(level);
  }

  /**
   * 获取当前日志级别
   * @returns {number} 当前日志级别
   */
  getLevel() {
    return this.level;
  }

  /**
   * 创建子logger
   * @param {string} prefix - 子logger前缀
   * @returns {Logger} 子logger实例
   */
  child(prefix) {
    return new Logger({
      level: this.level,
      enableColors: this.enableColors,
      prefix: this.prefix ? `${this.prefix}:${prefix}` : prefix
    });
  }
}

/**
 * 默认logger实例
 */
export const logger = new Logger();

/**
 * 创建新的logger实例
 * @param {Object} options - Logger选项
 * @returns {Logger} Logger实例
 */
export function createLogger(options) {
  return new Logger(options);
}

/**
 * 设置全局日志级别
 * @param {string|number} level - 日志级别
 */
export function setGlobalLogLevel(level) {
  logger.setLevel(level);
}

/**
 * 简化的日志函数（向后兼容）
 */
export const log = {
  error: (message, data) => logger.error(message, data),
  warn: (message, data) => logger.warn(message, data),
  info: (message, data) => logger.info(message, data),
  debug: (message, data) => logger.debug(message, data),
  trace: (message, data) => logger.trace(message, data)
};

// 导出默认logger的方法作为独立函数
export const { error, warn, info, debug, trace } = logger;
