/**
 * 文件编码检测工具
 * 
 * 提供文件编码检测和转换功能
 */

import { readFileSync } from 'fs';
import { logger } from './logger.js';

/**
 * 支持的编码类型
 */
export const ENCODINGS = {
  UTF8: 'utf8',
  UTF16LE: 'utf16le',
  UTF16BE: 'utf16be',
  ASCII: 'ascii',
  LATIN1: 'latin1',
  BASE64: 'base64',
  HEX: 'hex'
};

/**
 * BOM (Byte Order Mark) 标识
 */
const BOM_SIGNATURES = {
  [Buffer.from([0xEF, 0xBB, 0xBF]).toString('hex')]: ENCODINGS.UTF8,
  [Buffer.from([0xFF, 0xFE]).toString('hex')]: ENCODINGS.UTF16LE,
  [Buffer.from([0xFE, 0xFF]).toString('hex')]: ENCODINGS.UTF16BE
};

/**
 * 检测文件编码
 * @param {string} filePath - 文件路径
 * @returns {string} 检测到的编码
 */
export function detectEncoding(filePath) {
  try {
    // 读取文件的前几个字节来检测BOM
    const buffer = readFileSync(filePath);
    
    if (buffer.length === 0) {
      return ENCODINGS.UTF8; // 空文件默认为UTF-8
    }

    // 检查BOM
    const bomEncoding = detectBOM(buffer);
    if (bomEncoding) {
      logger.debug('通过BOM检测到编码', { filePath, encoding: bomEncoding });
      return bomEncoding;
    }

    // 检查是否为二进制文件
    if (isBinaryFile(buffer)) {
      logger.debug('检测到二进制文件', { filePath });
      return ENCODINGS.LATIN1; // 二进制文件使用latin1以保持字节完整性
    }

    // 尝试UTF-8解码
    try {
      const text = buffer.toString(ENCODINGS.UTF8);
      // 检查是否包含替换字符（表示解码失败）
      if (!text.includes('\uFFFD')) {
        logger.debug('检测到UTF-8编码', { filePath });
        return ENCODINGS.UTF8;
      }
    } catch (error) {
      // UTF-8解码失败
    }

    // 尝试其他编码
    const detectedEncoding = detectTextEncoding(buffer);
    logger.debug('检测到编码', { filePath, encoding: detectedEncoding });
    return detectedEncoding;

  } catch (error) {
    logger.warn('编码检测失败，使用默认UTF-8', { filePath, error: error.message });
    return ENCODINGS.UTF8;
  }
}

/**
 * 检测BOM标识
 * @param {Buffer} buffer - 文件缓冲区
 * @returns {string|null} 检测到的编码或null
 */
function detectBOM(buffer) {
  if (buffer.length < 2) return null;

  // 检查UTF-8 BOM (3字节)
  if (buffer.length >= 3) {
    const utf8Bom = buffer.subarray(0, 3).toString('hex');
    if (BOM_SIGNATURES[utf8Bom]) {
      return BOM_SIGNATURES[utf8Bom];
    }
  }

  // 检查UTF-16 BOM (2字节)
  const utf16Bom = buffer.subarray(0, 2).toString('hex');
  if (BOM_SIGNATURES[utf16Bom]) {
    return BOM_SIGNATURES[utf16Bom];
  }

  return null;
}

/**
 * 检测是否为二进制文件
 * @param {Buffer} buffer - 文件缓冲区
 * @returns {boolean} 是否为二进制文件
 */
function isBinaryFile(buffer) {
  // 检查前8192字节
  const sampleSize = Math.min(buffer.length, 8192);
  const sample = buffer.subarray(0, sampleSize);
  
  let nullBytes = 0;
  let controlChars = 0;
  
  for (let i = 0; i < sample.length; i++) {
    const byte = sample[i];
    
    // 检查null字节
    if (byte === 0) {
      nullBytes++;
    }
    
    // 检查控制字符（除了常见的文本控制字符）
    if (byte < 32 && byte !== 9 && byte !== 10 && byte !== 13) {
      controlChars++;
    }
  }
  
  // 如果null字节或控制字符比例过高，认为是二进制文件
  const nullRatio = nullBytes / sample.length;
  const controlRatio = controlChars / sample.length;
  
  return nullRatio > 0.01 || controlRatio > 0.05;
}

/**
 * 检测文本编码
 * @param {Buffer} buffer - 文件缓冲区
 * @returns {string} 检测到的编码
 */
function detectTextEncoding(buffer) {
  // 简单的启发式检测
  
  // 检查是否可能是UTF-16
  if (buffer.length >= 2 && buffer.length % 2 === 0) {
    let nullCount = 0;
    for (let i = 0; i < Math.min(buffer.length, 1000); i += 2) {
      if (buffer[i] === 0 || buffer[i + 1] === 0) {
        nullCount++;
      }
    }
    
    // 如果有很多null字节，可能是UTF-16
    if (nullCount > buffer.length / 20) {
      // 简单检测字节序
      let leScore = 0;
      let beScore = 0;
      
      for (let i = 0; i < Math.min(buffer.length - 1, 100); i += 2) {
        if (buffer[i] !== 0 && buffer[i + 1] === 0) leScore++;
        if (buffer[i] === 0 && buffer[i + 1] !== 0) beScore++;
      }
      
      if (leScore > beScore) {
        return ENCODINGS.UTF16LE;
      } else if (beScore > leScore) {
        return ENCODINGS.UTF16BE;
      }
    }
  }
  
  // 检查是否为纯ASCII
  let isAscii = true;
  for (let i = 0; i < Math.min(buffer.length, 1000); i++) {
    if (buffer[i] > 127) {
      isAscii = false;
      break;
    }
  }
  
  if (isAscii) {
    return ENCODINGS.ASCII;
  }
  
  // 默认返回UTF-8
  return ENCODINGS.UTF8;
}

/**
 * 转换文件编码
 * @param {string} filePath - 文件路径
 * @param {string} fromEncoding - 源编码
 * @param {string} toEncoding - 目标编码
 * @returns {string} 转换后的文本内容
 */
export function convertFileEncoding(filePath, fromEncoding, toEncoding) {
  try {
    const buffer = readFileSync(filePath);
    
    // 如果源编码和目标编码相同，直接返回
    if (fromEncoding === toEncoding) {
      return buffer.toString(fromEncoding);
    }
    
    // 先解码为字符串
    let text = buffer.toString(fromEncoding);
    
    // 如果目标编码是Buffer支持的编码，可以直接转换
    if (Object.values(ENCODINGS).includes(toEncoding)) {
      return Buffer.from(text, 'utf8').toString(toEncoding);
    }
    
    return text;
    
  } catch (error) {
    logger.error('编码转换失败', { 
      filePath, 
      fromEncoding, 
      toEncoding, 
      error: error.message 
    });
    throw error;
  }
}

/**
 * 验证文本是否为有效的指定编码
 * @param {string} text - 文本内容
 * @param {string} encoding - 编码类型
 * @returns {boolean} 是否为有效编码
 */
export function isValidEncoding(text, encoding) {
  try {
    const buffer = Buffer.from(text, encoding);
    const decoded = buffer.toString(encoding);
    return decoded === text;
  } catch (error) {
    return false;
  }
}

/**
 * 获取文本的字节长度（按指定编码）
 * @param {string} text - 文本内容
 * @param {string} encoding - 编码类型
 * @returns {number} 字节长度
 */
export function getByteLength(text, encoding = ENCODINGS.UTF8) {
  try {
    return Buffer.byteLength(text, encoding);
  } catch (error) {
    logger.warn('获取字节长度失败', { encoding, error: error.message });
    return text.length; // 回退到字符长度
  }
}

/**
 * 清理文本中的无效字符
 * @param {string} text - 文本内容
 * @returns {string} 清理后的文本
 */
export function cleanText(text) {
  // 移除替换字符（表示解码失败的字符）
  let cleaned = text.replace(/\uFFFD/g, '');
  
  // 移除其他控制字符（保留常见的换行符和制表符）
  cleaned = cleaned.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '');
  
  return cleaned;
}

/**
 * 检测文本中的换行符类型
 * @param {string} text - 文本内容
 * @returns {string} 换行符类型：'crlf', 'lf', 'cr', 'mixed'
 */
export function detectLineEndings(text) {
  const crlfCount = (text.match(/\r\n/g) || []).length;
  const lfCount = (text.match(/(?<!\r)\n/g) || []).length;
  const crCount = (text.match(/\r(?!\n)/g) || []).length;
  
  const total = crlfCount + lfCount + crCount;
  
  if (total === 0) return 'none';
  
  if (crlfCount === total) return 'crlf';
  if (lfCount === total) return 'lf';
  if (crCount === total) return 'cr';
  
  return 'mixed';
}

/**
 * 标准化换行符为LF
 * @param {string} text - 文本内容
 * @returns {string} 标准化后的文本
 */
export function normalizeLineEndings(text) {
  return text.replace(/\r\n/g, '\n').replace(/\r/g, '\n');
}
