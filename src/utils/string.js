/**
 * 字符串处理工具函数
 * 
 * 提供各种字符串操作、格式化、验证等功能
 * 
 * @original: Lodash字符串处理函数 (L1315-1633)
 */

import { logger } from './logger.js';

/**
 * 移除字符串末尾的空白字符
 * @original: BlB()函数 (L1315)
 * @param {string} str - 输入字符串
 * @returns {string} 处理后的字符串
 */
export function trimEnd(str) {
  if (!str) return str;
  
  let length = str.length;
  while (length-- && /\s/.test(str.charAt(length)));
  
  return str.slice(0, length + 1);
}

/**
 * 移除字符串开头和末尾的空白字符
 * @original: DlB()函数 (L1322)
 * @param {string} str - 输入字符串
 * @returns {string} 处理后的字符串
 */
export function trim(str) {
  if (!str) return str;
  
  return str.slice(0, trimEnd(str).length).replace(/^\s+/, '');
}

/**
 * 将字符串转换为数字
 * @original: WlB()函数 (L1331)
 * @param {*} value - 要转换的值
 * @returns {number} 转换后的数字
 */
export function toNumber(value) {
  if (typeof value === 'number') return value;
  
  if (isSymbol(value)) return NaN;
  
  if (isObject(value)) {
    const other = typeof value.valueOf === 'function' ? value.valueOf() : value;
    value = isObject(other) ? other + '' : other;
  }
  
  if (typeof value !== 'string') return value === 0 ? value : +value;
  
  value = trim(value);
  const isBinary = /^0b[01]+$/i.test(value);
  
  return isBinary || /^0o[0-7]+$/i.test(value)
    ? parseInt(value.slice(2), isBinary ? 2 : 8)
    : /^[-+]0x[0-9a-f]+$/i.test(value) ? NaN : +value;
}

/**
 * 将值转换为有限数字
 * @original: XlB()函数 (L1346)
 * @param {*} value - 要转换的值
 * @returns {number} 转换后的有限数字
 */
export function toFinite(value) {
  if (!value) return value === 0 ? value : 0;
  
  value = toNumber(value);
  
  if (value === Infinity || value === -Infinity) {
    const sign = value < 0 ? -1 : 1;
    return sign * Number.MAX_VALUE;
  }
  
  return value === value ? value : 0;
}

/**
 * 将值转换为整数
 * @original: VlB()函数 (L1355)
 * @param {*} value - 要转换的值
 * @returns {number} 转换后的整数
 */
export function toInteger(value) {
  const result = toFinite(value);
  const remainder = result % 1;
  
  return remainder ? result - remainder : result;
}

/**
 * 检查值是否为Symbol类型
 * @param {*} value - 要检查的值
 * @returns {boolean} 是否为Symbol
 */
function isSymbol(value) {
  return typeof value === 'symbol' || 
    (isObjectLike(value) && Object.prototype.toString.call(value) === '[object Symbol]');
}

/**
 * 检查值是否为对象
 * @param {*} value - 要检查的值
 * @returns {boolean} 是否为对象
 */
function isObject(value) {
  const type = typeof value;
  return value != null && (type === 'object' || type === 'function');
}

/**
 * 检查值是否为类对象
 * @param {*} value - 要检查的值
 * @returns {boolean} 是否为类对象
 */
function isObjectLike(value) {
  return value != null && typeof value === 'object';
}

/**
 * 检查字符串是否包含Unicode字符
 * @original: OnB()函数 (L1583)
 * @param {string} str - 要检查的字符串
 * @returns {boolean} 是否包含Unicode字符
 */
export function hasUnicodeWord(str) {
  const reHasUnicodeWord = /[a-z][A-Z]|[A-Z]{2}[a-z]|[0-9][A-Za-z]|[A-Za-z][0-9]|[^a-zA-Z0-9 ]/;
  return reHasUnicodeWord.test(str);
}

/**
 * 将字符串分割为字符数组（支持Unicode）
 * @original: mnB()函数 (L1615)
 * @param {string} str - 要分割的字符串
 * @returns {string[]} 字符数组
 */
export function stringToArray(str) {
  if (hasUnicodeWord(str)) {
    // 支持Unicode的分割
    const reUnicodeWord = /[^\x00-\x2f\x3a-\x40\x5b-\x60\x7b-\x7f]+/g;
    return str.match(reUnicodeWord) || [];
  }
  
  return str.split('');
}

/**
 * 将字符串转换为驼峰命名
 * @original: lnB()函数 (L1631)
 * @param {string} str - 输入字符串
 * @returns {string} 驼峰命名字符串
 */
export function camelCase(str) {
  const words = getWords(str.toLowerCase());
  
  return words.reduce((result, word, index) => {
    if (index === 0) {
      return word;
    }
    return result + capitalize(word);
  }, '');
}

/**
 * 首字母大写
 * @param {string} str - 输入字符串
 * @returns {string} 首字母大写的字符串
 */
export function capitalize(str) {
  if (!str) return str;
  return str.charAt(0).toUpperCase() + str.slice(1);
}

/**
 * 从字符串中提取单词
 * @param {string} str - 输入字符串
 * @returns {string[]} 单词数组
 */
function getWords(str) {
  const reWords = /[A-Z]?[a-z]+|[A-Z]+(?=[A-Z][a-z]|\b)|[0-9]+/g;
  return str.match(reWords) || [];
}

/**
 * 将字符串转换为kebab-case
 * @param {string} str - 输入字符串
 * @returns {string} kebab-case字符串
 */
export function kebabCase(str) {
  const words = getWords(str.toLowerCase());
  return words.join('-');
}

/**
 * 将字符串转换为snake_case
 * @param {string} str - 输入字符串
 * @returns {string} snake_case字符串
 */
export function snakeCase(str) {
  const words = getWords(str.toLowerCase());
  return words.join('_');
}

/**
 * 将字符串转换为PascalCase
 * @param {string} str - 输入字符串
 * @returns {string} PascalCase字符串
 */
export function pascalCase(str) {
  const words = getWords(str.toLowerCase());
  return words.map(capitalize).join('');
}

/**
 * 限制数值在指定范围内
 * @original: pnB()函数 (L1635)
 * @param {number} number - 输入数值
 * @param {number} lower - 下限
 * @param {number} upper - 上限
 * @returns {number} 限制后的数值
 */
export function clamp(number, lower, upper) {
  if (number === number) {
    if (upper !== undefined) {
      number = number <= upper ? number : upper;
    }
    if (lower !== undefined) {
      number = number >= lower ? number : lower;
    }
  }
  return number;
}

/**
 * 生成随机字符串
 * @param {number} length - 字符串长度
 * @param {string} chars - 可用字符集
 * @returns {string} 随机字符串
 */
export function randomString(length = 8, chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789') {
  let result = '';
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

/**
 * 截断字符串
 * @param {string} str - 输入字符串
 * @param {number} length - 最大长度
 * @param {string} omission - 省略符号
 * @returns {string} 截断后的字符串
 */
export function truncate(str, length = 30, omission = '...') {
  if (!str || str.length <= length) return str;
  
  return str.slice(0, length - omission.length) + omission;
}

/**
 * 填充字符串到指定长度
 * @param {string} str - 输入字符串
 * @param {number} length - 目标长度
 * @param {string} chars - 填充字符
 * @returns {string} 填充后的字符串
 */
export function padStart(str, length, chars = ' ') {
  if (!str) str = '';
  
  const strLength = str.length;
  if (strLength >= length) return str;
  
  const padLength = length - strLength;
  const pad = chars.repeat(Math.ceil(padLength / chars.length)).slice(0, padLength);
  
  return pad + str;
}

/**
 * 在字符串末尾填充
 * @param {string} str - 输入字符串
 * @param {number} length - 目标长度
 * @param {string} chars - 填充字符
 * @returns {string} 填充后的字符串
 */
export function padEnd(str, length, chars = ' ') {
  if (!str) str = '';
  
  const strLength = str.length;
  if (strLength >= length) return str;
  
  const padLength = length - strLength;
  const pad = chars.repeat(Math.ceil(padLength / chars.length)).slice(0, padLength);
  
  return str + pad;
}

/**
 * 重复字符串
 * @param {string} str - 输入字符串
 * @param {number} n - 重复次数
 * @returns {string} 重复后的字符串
 */
export function repeat(str, n) {
  if (!str || n < 1) return '';
  return str.repeat(n);
}

/**
 * 转义HTML字符
 * @param {string} str - 输入字符串
 * @returns {string} 转义后的字符串
 */
export function escapeHtml(str) {
  if (!str) return str;
  
  const htmlEscapes = {
    '&': '&amp;',
    '<': '&lt;',
    '>': '&gt;',
    '"': '&quot;',
    "'": '&#39;'
  };
  
  return str.replace(/[&<>"']/g, match => htmlEscapes[match]);
}

/**
 * 反转义HTML字符
 * @param {string} str - 输入字符串
 * @returns {string} 反转义后的字符串
 */
export function unescapeHtml(str) {
  if (!str) return str;
  
  const htmlUnescapes = {
    '&amp;': '&',
    '&lt;': '<',
    '&gt;': '>',
    '&quot;': '"',
    '&#39;': "'"
  };
  
  return str.replace(/&(?:amp|lt|gt|quot|#39);/g, match => htmlUnescapes[match]);
}

/**
 * 检查字符串是否以指定前缀开始
 * @param {string} str - 输入字符串
 * @param {string} target - 目标前缀
 * @param {number} position - 开始位置
 * @returns {boolean} 是否以指定前缀开始
 */
export function startsWith(str, target, position = 0) {
  if (!str) return false;
  return str.slice(position, position + target.length) === target;
}

/**
 * 检查字符串是否以指定后缀结束
 * @param {string} str - 输入字符串
 * @param {string} target - 目标后缀
 * @param {number} position - 结束位置
 * @returns {boolean} 是否以指定后缀结束
 */
export function endsWith(str, target, position) {
  if (!str) return false;
  
  const length = str.length;
  position = position === undefined ? length : position;
  
  if (position < 0 || position != position) {
    position = 0;
  } else if (position > length) {
    position = length;
  }
  
  const end = position;
  position -= target.length;
  
  return position >= 0 && str.slice(position, end) === target;
}
