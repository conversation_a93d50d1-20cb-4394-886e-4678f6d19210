/**
 * CLI应用程序创建和配置
 * 
 * 负责创建Commander.js CLI应用程序，配置所有命令和选项
 * 
 * @original: ER8()函数 (L57283-57924)
 */

import { Command, Option } from 'commander';
import { handleMainCommand } from './commands/main.js';
import { handleMcpCommands } from './commands/mcp.js';
import { handleInstallCommand } from './commands/install.js';
import { handleUpdateCommand } from './commands/update.js';
import { handleDoctorCommand } from './commands/doctor.js';
import { handleLoginCommand } from './commands/login.js';
import { handleMigrateCommand } from './commands/migrate.js';
import { PERMISSION_MODES } from '../config/constants.js';
import { logger } from '../utils/logger.js';

/**
 * 创建主CLI应用程序
 * 
 * @original: ER8()函数的主要逻辑
 * @returns {Command} Commander.js应用程序实例
 */
export async function createCLI() {
  const app = new Command();
  
  // 基本应用配置
  app
    .name('claude')
    .description('Claude Code - starts an interactive session by default, use -p/--print for non-interactive output')
    .argument('[prompt]', 'Your prompt', String)
    .helpOption('-h, --help', 'Display help for command');

  // 调试和输出选项
  app
    .option('-d, --debug', 'Enable debug mode', () => true)
    .addOption(new Option('-d2e, --debug-to-stderr', 'Enable debug mode (to stderr)')
      .argParser(Boolean)
      .hideHelp())
    .option('--verbose', 'Override verbose mode setting from config', () => true);

  // 输出格式选项
  app
    .option('-p, --print', 'Print response and exit (useful for pipes)', () => true)
    .addOption(new Option('--output-format <format>', 
      'Output format (only works with --print): "text" (default), "json" (single result), or "stream-json" (realtime streaming)')
      .choices(['text', 'json', 'stream-json']))
    .addOption(new Option('--input-format <format>',
      'Input format (only works with --print): "text" (default), or "stream-json" (realtime streaming input)')
      .choices(['text', 'stream-json']));

  // 权限和安全选项
  app
    .option('--dangerously-skip-permissions', 
      'Bypass all permission checks. Recommended only for sandboxes with no internet access.', 
      () => true)
    .addOption(new Option('--permission-mode <mode>', 'Permission mode to use for the session')
      .argParser(String)
      .choices(PERMISSION_MODES));

  // 工具控制选项
  app
    .option('--allowedTools <tools...>', 
      'Comma or space-separated list of tool names to allow (e.g. "Bash(git:*) Edit")')
    .option('--disallowedTools <tools...>', 
      'Comma or space-separated list of tool names to deny (e.g. "Bash(git:*) Edit")');

  // 模型配置选项
  app
    .option('--model <model>', 
      'Model for the current session. Provide an alias for the latest model (e.g. \'sonnet\' or \'opus\') or a model\'s full name (e.g. \'claude-sonnet-4-20250514\').')
    .option('--fallback-model <model>', 
      'Enable automatic fallback to specified model when default model is overloaded (only works with --print)');

  // 会话控制选项
  app
    .option('-c, --continue', 'Continue the most recent conversation', () => true)
    .option('-r, --resume [sessionId]', 
      'Resume a conversation - provide a session ID or interactively select a conversation to resume', 
      value => value || true)
    .option('--session-id <uuid>', 
      'Use a specific session ID for the conversation (must be a valid UUID)');

  // MCP和配置选项
  app
    .option('--mcp-config <file or string>', 'Load MCP servers from a JSON file or string')
    .option('--strict-mcp-config', 
      'Only use MCP servers from --mcp-config, ignoring all other MCP configurations', 
      () => true)
    .option('--settings <file-or-json>', 
      'Path to a settings JSON file or a JSON string to load additional settings from');

  // 其他选项
  app
    .option('--add-dir <directories...>', 'Additional directories to allow tool access to')
    .option('--ide', 
      'Automatically connect to IDE on startup if exactly one valid IDE is available', 
      () => true);

  // 主命令处理器
  app.action(handleMainCommand);

  // 子命令
  setupSubCommands(app);

  return app;
}

/**
 * 设置所有子命令
 * 
 * @param {Command} app - Commander.js应用程序实例
 */
function setupSubCommands(app) {
  // MCP命令
  const mcpCommand = app.command('mcp')
    .description('Configure and manage MCP servers')
    .helpOption('-h, --help', 'Display help for command');
  
  handleMcpCommands(mcpCommand);

  // 其他命令
  app.command('login')
    .description('Login to Claude account')
    .helpOption('-h, --help', 'Display help for command')
    .action(handleLoginCommand);

  app.command('update')
    .description('Check for updates and install if available')
    .helpOption('-h, --help', 'Display help for command')
    .action(handleUpdateCommand);

  app.command('install [target]')
    .description('Install Claude Code native build. Use [target] to specify version (stable, latest, or specific version)')
    .option('--force', 'Force installation even if already installed')
    .helpOption('-h, --help', 'Display help for command')
    .action(handleInstallCommand);

  app.command('doctor')
    .description('Check the health of your Claude Code auto-updater')
    .helpOption('-h, --help', 'Display help for command')
    .action(handleDoctorCommand);

  app.command('migrate-installer')
    .description('Migrate from global npm installation to local installation')
    .helpOption('-h, --help', 'Display help for command')
    .action(handleMigrateCommand);
}
