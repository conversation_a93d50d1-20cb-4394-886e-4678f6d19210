/**
 * 命令行接口管理
 * 
 * 基于Commander.js的命令行参数解析和命令处理
 * 
 * @original: 命令行相关代码 (L57284-57924, L54155-54166)
 */

import { logger } from '../utils/logger.js';
import { APP_INFO } from '../config/constants.js';
import { logTelemetryEvent } from '../services/telemetry.js';

/**
 * 命令类型枚举
 */
export const COMMAND_TYPES = {
  MAIN: 'main',
  CONFIG: 'config',
  MCP: 'mcp',
  INSTALL: 'install',
  UPDATE: 'update',
  DOCTOR: 'doctor',
  MIGRATE: 'migrate',
  SETUP_TOKEN: 'setup-token'
};

/**
 * 权限模式枚举
 * @original: _K1数组
 */
export const PERMISSION_MODES = {
  ASK: 'ask',
  ALLOW: 'allow',
  DENY: 'deny',
  BYPASS: 'bypassPermissions'
};

/**
 * 输出格式枚举
 */
export const OUTPUT_FORMATS = {
  TEXT: 'text',
  JSON: 'json',
  STREAM_JSON: 'stream-json'
};

/**
 * 输入格式枚举
 */
export const INPUT_FORMATS = {
  TEXT: 'text',
  STREAM_JSON: 'stream-json'
};

/**
 * 命令行程序类
 */
export class CLIProgram {
  constructor() {
    this.program = null;
    this.commands = new Map();
    this.options = new Map();
    this.middlewares = [];
  }

  /**
   * 创建主程序
   * @original: 主程序创建逻辑 (L57284-57632)
   * @returns {Object} Commander程序实例
   */
  createProgram() {
    const { Command, Option } = require('commander');
    
    const program = new Command();
    
    // 基础配置
    program
      .name('claude')
      .description('Claude Code - starts an interactive session by default, use -p/--print for non-interactive output')
      .argument('[prompt]', 'Your prompt', String)
      .helpOption('-h, --help', 'Display help for command')
      .version(`${APP_INFO.VERSION} (Claude Code)`, '-v, --version', 'Output the version number');

    // 添加主要选项
    this.addMainOptions(program);
    
    // 添加子命令
    this.addSubCommands(program);

    // 设置主动作
    this.setMainAction(program);

    this.program = program;
    return program;
  }

  /**
   * 添加主要选项
   * @param {Object} program - Commander程序实例
   */
  addMainOptions(program) {
    const { Option } = require('commander');

    // 调试选项
    program
      .option('-d, --debug', 'Enable debug mode', () => true)
      .addOption(new Option('-d2e, --debug-to-stderr', 'Enable debug mode (to stderr)')
        .argParser(Boolean)
        .hideHelp())
      .option('--verbose', 'Override verbose mode setting from config', () => true);

    // 输出选项
    program
      .option('-p, --print', 'Print response and exit (useful for pipes)', () => true)
      .addOption(new Option('--output-format <format>', 'Output format (only works with --print): "text" (default), "json" (single result), or "stream-json" (realtime streaming)')
        .choices(Object.values(OUTPUT_FORMATS)))
      .addOption(new Option('--input-format <format>', 'Input format (only works with --print): "text" (default), or "stream-json" (realtime streaming input)')
        .choices(Object.values(INPUT_FORMATS)));

    // 权限选项
    program
      .option('--dangerously-skip-permissions', 'Bypass all permission checks. Recommended only for sandboxes with no internet access.', () => true)
      .addOption(new Option('--permission-mode <mode>', 'Permission mode to use for the session')
        .argParser(String)
        .choices(Object.values(PERMISSION_MODES)));

    // 工具选项
    program
      .option('--allowedTools <tools...>', 'Comma or space-separated list of tool names to allow (e.g. "Bash(git:*) Edit")')
      .option('--disallowedTools <tools...>', 'Comma or space-separated list of tool names to deny (e.g. "Bash(git:*) Edit")');

    // 模型选项
    program
      .option('--model <model>', 'Model for the current session. Provide an alias for the latest model (e.g. \'sonnet\' or \'opus\') or a model\'s full name (e.g. \'claude-sonnet-4-20250514\').')
      .option('--fallback-model <model>', 'Enable automatic fallback to specified model when default model is overloaded (only works with --print)');

    // 会话选项
    program
      .option('-c, --continue', 'Continue the most recent conversation', () => true)
      .option('-r, --resume [sessionId]', 'Resume a conversation - provide a session ID or interactively select a conversation to resume', value => value || true)
      .option('--session-id <uuid>', 'Use a specific session ID for the conversation (must be a valid UUID)');

    // MCP选项
    program
      .option('--mcp-config <file or string>', 'Load MCP servers from a JSON file or string')
      .option('--strict-mcp-config', 'Only use MCP servers from --mcp-config, ignoring all other MCP configurations', () => true);

    // 其他选项
    program
      .option('--settings <file-or-json>', 'Path to a settings JSON file or a JSON string to load additional settings from')
      .option('--add-dir <directories...>', 'Additional directories to allow tool access to')
      .option('--ide', 'Automatically connect to IDE on startup if exactly one valid IDE is available', () => true);

    // 隐藏选项
    program
      .addOption(new Option('--teleport [session]', 'Resume a teleport session, optionally specify session ID').hideHelp())
      .addOption(new Option('--remote <description>', 'Create a remote session with the given description').hideHelp())
      .addOption(new Option('--max-turns <turns>', 'Maximum number of agentic turns in non-interactive mode. This will early exit the conversation after the specified number of turns. (only works with --print)')
        .argParser(Number)
        .hideHelp())
      .addOption(new Option('--permission-prompt-tool <tool>', 'MCP tool to use for permission prompts (only works with --print)')
        .argParser(String)
        .hideHelp())
      .addOption(new Option('--system-prompt <prompt>', 'System prompt to use for the session  (only works with --print)')
        .argParser(String)
        .hideHelp())
      .addOption(new Option('--system-prompt-file <file>', 'Read system prompt from a file (only works with --print)')
        .argParser(String)
        .hideHelp())
      .addOption(new Option('--append-system-prompt <prompt>', 'Append a system prompt to the default system prompt')
        .argParser(String));

    // 废弃选项
    program
      .option('--mcp-debug', '[DEPRECATED. Use --debug instead] Enable MCP debug mode (shows MCP server errors)', () => true);
  }

  /**
   * 添加子命令
   * @param {Object} program - Commander程序实例
   */
  addSubCommands(program) {
    this.addConfigCommands(program);
    this.addMCPCommands(program);
    this.addMaintenanceCommands(program);
  }

  /**
   * 添加配置命令
   * @original: 配置命令逻辑 (L57633-57685)
   * @param {Object} program - Commander程序实例
   */
  addConfigCommands(program) {
    const configCmd = program
      .command('config')
      .description('Manage configuration (eg. claude config set -g theme dark)')
      .helpOption('-h, --help', 'Display help for command');

    // config get
    configCmd
      .command('get <key>')
      .description('Get a config value')
      .option('-g, --global', 'Use global config')
      .helpOption('-h, --help', 'Display help for command')
      .action(async (key, options) => {
        await this.handleConfigGet(key, options);
      });

    // config set
    configCmd
      .command('set <key> <value>')
      .description('Set a config value')
      .option('-g, --global', 'Use global config')
      .helpOption('-h, --help', 'Display help for command')
      .action(async (key, value, options) => {
        await this.handleConfigSet(key, value, options);
      });

    // config remove
    configCmd
      .command('remove <key> [values...]')
      .alias('rm')
      .description('Remove a config value or items from a config array')
      .option('-g, --global', 'Use global config')
      .helpOption('-h, --help', 'Display help for command')
      .action(async (key, values, options) => {
        await this.handleConfigRemove(key, values, options);
      });

    // config list
    configCmd
      .command('list')
      .alias('ls')
      .description('List all config values')
      .option('-g, --global', 'Use global config', false)
      .helpOption('-h, --help', 'Display help for command')
      .action(async (options) => {
        await this.handleConfigList(options);
      });

    // config add
    configCmd
      .command('add <key> <values...>')
      .description('Add items to a config array (space or comma separated)')
      .option('-g, --global', 'Use global config')
      .helpOption('-h, --help', 'Display help for command')
      .action(async (key, values, options) => {
        await this.handleConfigAdd(key, values, options);
      });
  }

  /**
   * 添加MCP命令
   * @original: MCP命令逻辑 (L57686-57844)
   * @param {Object} program - Commander程序实例
   */
  addMCPCommands(program) {
    const mcpCmd = program
      .command('mcp')
      .description('Configure and manage MCP servers')
      .helpOption('-h, --help', 'Display help for command');

    // mcp serve
    mcpCmd
      .command('serve')
      .description('Start the Claude Code MCP server')
      .helpOption('-h, --help', 'Display help for command')
      .option('-d, --debug', 'Enable debug mode', () => true)
      .option('--verbose', 'Override verbose mode setting from config', () => true)
      .action(async (options) => {
        await this.handleMCPServe(options);
      });

    // mcp add
    mcpCmd
      .command('add <name> <commandOrUrl> [args...]')
      .description('Add a server')
      .option('-s, --scope <scope>', 'Configuration scope (local, user, or project)', 'local')
      .option('-t, --transport <transport>', 'Transport type (stdio, sse, http)', 'stdio')
      .option('-e, --env <env...>', 'Set environment variables (e.g. -e KEY=value)')
      .option('-H, --header <header...>', 'Set HTTP headers for SSE and HTTP transports (e.g. -H "X-Api-Key: abc123" -H "X-Custom: value")')
      .helpOption('-h, --help', 'Display help for command')
      .action(async (name, commandOrUrl, args, options) => {
        await this.handleMCPAdd(name, commandOrUrl, args, options);
      });

    // mcp remove
    mcpCmd
      .command('remove <name>')
      .description('Remove an MCP server')
      .option('-s, --scope <scope>', 'Configuration scope (local, user, or project) - if not specified, removes from whichever scope it exists in')
      .helpOption('-h, --help', 'Display help for command')
      .action(async (name, options) => {
        await this.handleMCPRemove(name, options);
      });

    // mcp list
    mcpCmd
      .command('list')
      .description('List configured MCP servers')
      .helpOption('-h, --help', 'Display help for command')
      .action(async () => {
        await this.handleMCPList();
      });

    // mcp get
    mcpCmd
      .command('get <name>')
      .description('Get details about an MCP server')
      .helpOption('-h, --help', 'Display help for command')
      .action(async (name) => {
        await this.handleMCPGet(name);
      });

    // mcp add-json
    mcpCmd
      .command('add-json <name> <json>')
      .description('Add an MCP server (stdio or SSE) with a JSON string')
      .option('-s, --scope <scope>', 'Configuration scope (local, user, or project)', 'local')
      .helpOption('-h, --help', 'Display help for command')
      .action(async (name, json, options) => {
        await this.handleMCPAddJSON(name, json, options);
      });

    // mcp add-from-claude-desktop
    mcpCmd
      .command('add-from-claude-desktop')
      .description('Import MCP servers from Claude Desktop (Mac and WSL only)')
      .option('-s, --scope <scope>', 'Configuration scope (local, user, or project)', 'local')
      .helpOption('-h, --help', 'Display help for command')
      .action(async (options) => {
        await this.handleMCPImportFromClaudeDesktop(options);
      });

    // mcp reset-project-choices
    mcpCmd
      .command('reset-project-choices')
      .description('Reset all approved and rejected project-scoped (.mcp.json) servers within this project')
      .helpOption('-h, --help', 'Display help for command')
      .action(async () => {
        await this.handleMCPResetProjectChoices();
      });
  }

  /**
   * 添加维护命令
   * @original: 维护命令逻辑 (L57878-57924)
   * @param {Object} program - Commander程序实例
   */
  addMaintenanceCommands(program) {
    // migrate-installer
    program
      .command('migrate-installer')
      .description('Migrate from global npm installation to local installation')
      .helpOption('-h, --help', 'Display help for command')
      .action(async () => {
        await this.handleMigrateInstaller();
      });

    // setup-token
    program
      .command('setup-token')
      .description('Set up a long-lived authentication token (requires Claude subscription)')
      .helpOption('-h, --help', 'Display help for command')
      .action(async () => {
        await this.handleSetupToken();
      });

    // doctor
    program
      .command('doctor')
      .description('Check the health of your Claude Code auto-updater')
      .helpOption('-h, --help', 'Display help for command')
      .action(async () => {
        await this.handleDoctor();
      });

    // update
    program
      .command('update')
      .description('Check for updates and install if available')
      .helpOption('-h, --help', 'Display help for command')
      .action(async () => {
        await this.handleUpdate();
      });

    // install
    program
      .command('install [target]')
      .description('Install Claude Code native build. Use [target] to specify version (stable, latest, or specific version)')
      .option('--force', 'Force installation even if already installed')
      .helpOption('-h, --help', 'Display help for command')
      .action(async (target, options) => {
        await this.handleInstall(target, options);
      });
  }

  /**
   * 设置主动作
   * @param {Object} program - Commander程序实例
   */
  setMainAction(program) {
    program.action(async (prompt, options) => {
      await this.handleMainCommand(prompt, options);
    });
  }

  /**
   * 处理主命令
   * @param {string} prompt - 提示内容
   * @param {Object} options - 命令选项
   */
  async handleMainCommand(prompt, options) {
    try {
      logTelemetryEvent('tengu_init', {
        entrypoint: 'claude',
        hasPrompt: !!prompt,
        printMode: !!options.print,
        debugMode: !!options.debug
      });

      // 验证Node.js版本
      this.validateNodeVersion();

      // 处理环境设置
      this.setupEnvironment(options);

      // 启动主应用逻辑
      await this.startMainApplication(prompt, options);

    } catch (error) {
      logger.error('主命令执行失败', { error: error.message });
      process.exit(1);
    }
  }

  /**
   * 验证Node.js版本
   * @original: Node.js版本检查逻辑 (L57176-57177)
   */
  validateNodeVersion() {
    const nodeVersion = process.version.match(/^v(\d+)\./)?.[1];
    
    if (!nodeVersion || parseInt(nodeVersion) < 18) {
      console.error('Error: Claude Code requires Node.js version 18 or higher.');
      process.exit(1);
    }
  }

  /**
   * 设置环境
   * @param {Object} options - 命令选项
   */
  setupEnvironment(options) {
    // 设置入口点
    if (!process.env.CLAUDE_CODE_ENTRYPOINT) {
      process.env.CLAUDE_CODE_ENTRYPOINT = 'cli';
    }

    // 设置调试模式
    if (options.debug || options.debugToStderr) {
      process.env.DEBUG = 'true';
    }

    // 检查CI环境
    if (this.isCIEnvironment()) {
      console.warn('Running in CI environment - interactive features are limited');
    }
  }

  /**
   * 检查是否为CI环境
   * @returns {boolean} 是否为CI环境
   */
  isCIEnvironment() {
    return !!(process.env.CI || process.env.CONTINUOUS_INTEGRATION);
  }

  /**
   * 启动主应用逻辑
   * @param {string} prompt - 提示内容
   * @param {Object} options - 命令选项
   */
  async startMainApplication(prompt, options) {
    // 这里需要导入并启动主应用逻辑
    logger.info('启动Claude Code主应用', { 
      hasPrompt: !!prompt,
      printMode: !!options.print 
    });
  }

  /**
   * 处理配置获取命令
   * @param {string} key - 配置键
   * @param {Object} options - 命令选项
   */
  async handleConfigGet(key, options) {
    logTelemetryEvent('tengu_config_get', { key });
    
    // 实现配置获取逻辑
    logger.info('获取配置', { key, global: options.global });
  }

  /**
   * 处理配置设置命令
   * @param {string} key - 配置键
   * @param {string} value - 配置值
   * @param {Object} options - 命令选项
   */
  async handleConfigSet(key, value, options) {
    logTelemetryEvent('tengu_config_set', { key });
    
    // 实现配置设置逻辑
    logger.info('设置配置', { key, value, global: options.global });
  }

  /**
   * 处理配置移除命令
   * @param {string} key - 配置键
   * @param {Array} values - 要移除的值
   * @param {Object} options - 命令选项
   */
  async handleConfigRemove(key, values, options) {
    logTelemetryEvent('tengu_config_remove', { key });
    
    // 实现配置移除逻辑
    logger.info('移除配置', { key, values, global: options.global });
  }

  /**
   * 处理配置列表命令
   * @param {Object} options - 命令选项
   */
  async handleConfigList(options) {
    logTelemetryEvent('tengu_config_list', {});
    
    // 实现配置列表逻辑
    logger.info('列出配置', { global: options.global });
  }

  /**
   * 处理配置添加命令
   * @param {string} key - 配置键
   * @param {Array} values - 要添加的值
   * @param {Object} options - 命令选项
   */
  async handleConfigAdd(key, values, options) {
    logTelemetryEvent('tengu_config_add', { key });
    
    // 实现配置添加逻辑
    logger.info('添加配置', { key, values, global: options.global });
  }

  /**
   * 处理MCP服务命令
   * @param {Object} options - 命令选项
   */
  async handleMCPServe(options) {
    try {
      // 实现MCP服务启动逻辑
      logger.info('启动MCP服务器', options);
    } catch (error) {
      console.error('Error: Failed to start MCP server:', error.message);
      process.exit(1);
    }
  }

  /**
   * 处理MCP添加命令
   * @param {string} name - 服务器名称
   * @param {string} commandOrUrl - 命令或URL
   * @param {Array} args - 参数
   * @param {Object} options - 命令选项
   */
  async handleMCPAdd(name, commandOrUrl, args, options) {
    if (!name) {
      console.error('Error: Server name is required.');
      console.error('Usage: claude mcp add <name> <command> [args...]');
      process.exit(1);
    }

    if (!commandOrUrl) {
      console.error('Error: Command is required when server name is provided.');
      console.error('Usage: claude mcp add <name> <command> [args...]');
      process.exit(1);
    }

    try {
      // 实现MCP服务器添加逻辑
      logger.info('添加MCP服务器', { name, commandOrUrl, args, options });
    } catch (error) {
      console.error(error.message);
      process.exit(1);
    }
  }

  /**
   * 处理MCP移除命令
   * @param {string} name - 服务器名称
   * @param {Object} options - 命令选项
   */
  async handleMCPRemove(name, options) {
    try {
      // 实现MCP服务器移除逻辑
      logger.info('移除MCP服务器', { name, options });
    } catch (error) {
      console.error(error.message);
      process.exit(1);
    }
  }

  /**
   * 处理MCP列表命令
   */
  async handleMCPList() {
    logTelemetryEvent('tengu_mcp_list', {});
    
    // 实现MCP服务器列表逻辑
    logger.info('列出MCP服务器');
  }

  /**
   * 处理MCP获取命令
   * @param {string} name - 服务器名称
   */
  async handleMCPGet(name) {
    logTelemetryEvent('tengu_mcp_get', { name });
    
    // 实现MCP服务器详情获取逻辑
    logger.info('获取MCP服务器详情', { name });
  }

  /**
   * 处理MCP JSON添加命令
   * @param {string} name - 服务器名称
   * @param {string} json - JSON配置
   * @param {Object} options - 命令选项
   */
  async handleMCPAddJSON(name, json, options) {
    try {
      // 实现MCP JSON添加逻辑
      logger.info('通过JSON添加MCP服务器', { name, options });
    } catch (error) {
      console.error(error.message);
      process.exit(1);
    }
  }

  /**
   * 处理从Claude Desktop导入MCP命令
   * @param {Object} options - 命令选项
   */
  async handleMCPImportFromClaudeDesktop(options) {
    try {
      // 实现从Claude Desktop导入逻辑
      logger.info('从Claude Desktop导入MCP服务器', options);
    } catch (error) {
      console.error(error.message);
      process.exit(1);
    }
  }

  /**
   * 处理MCP重置项目选择命令
   */
  async handleMCPResetProjectChoices() {
    logTelemetryEvent('tengu_mcp_reset_mcpjson_choices', {});
    
    // 实现MCP项目选择重置逻辑
    logger.info('重置MCP项目选择');
  }

  /**
   * 处理迁移安装器命令
   * @original: 迁移安装器逻辑 (L57878-57887)
   */
  async handleMigrateInstaller() {
    logTelemetryEvent('tengu_migrate_installer_command', {});
    
    // 实现迁移安装器逻辑
    logger.info('迁移安装器');
  }

  /**
   * 处理设置token命令
   * @original: 设置token逻辑 (L57888-57902)
   */
  async handleSetupToken() {
    logTelemetryEvent('tengu_setup_token_command', {});
    
    // 实现设置token逻辑
    logger.info('设置认证token');
  }

  /**
   * 处理doctor命令
   * @original: doctor命令逻辑 (L57903-57914)
   */
  async handleDoctor() {
    logTelemetryEvent('tengu_doctor_command', {});
    
    // 实现健康检查逻辑
    logger.info('执行健康检查');
  }

  /**
   * 处理更新命令
   */
  async handleUpdate() {
    // 实现更新检查逻辑
    logger.info('检查更新');
  }

  /**
   * 处理安装命令
   * @param {string} target - 目标版本
   * @param {Object} options - 命令选项
   */
  async handleInstall(target, options) {
    // 实现安装逻辑
    logger.info('安装Claude Code', { target, force: options.force });
  }

  /**
   * 解析命令行参数
   * @param {Array} argv - 命令行参数
   * @returns {Promise<Object>} 解析结果
   */
  async parseArguments(argv = process.argv) {
    if (!this.program) {
      this.createProgram();
    }

    try {
      await this.program.parseAsync(argv);
      return this.program;
    } catch (error) {
      logger.error('命令行参数解析失败', { error: error.message });
      throw error;
    }
  }

  /**
   * 添加中间件
   * @param {Function} middleware - 中间件函数
   */
  addMiddleware(middleware) {
    this.middlewares.push(middleware);
  }

  /**
   * 执行中间件
   * @param {Object} context - 执行上下文
   * @returns {Promise<Object>} 处理后的上下文
   */
  async executeMiddlewares(context) {
    let currentContext = context;

    for (const middleware of this.middlewares) {
      try {
        currentContext = await middleware(currentContext);
      } catch (error) {
        logger.error('中间件执行失败', { error: error.message });
        throw error;
      }
    }

    return currentContext;
  }

  /**
   * 获取程序实例
   * @returns {Object} Commander程序实例
   */
  getProgram() {
    if (!this.program) {
      this.createProgram();
    }
    
    return this.program;
  }
}

/**
 * 参数解析器类
 */
export class ArgumentParser {
  /**
   * 解析工具列表
   * @param {Array} tools - 工具列表
   * @returns {Array} 解析后的工具列表
   */
  parseToolsList(tools) {
    if (!tools || !Array.isArray(tools)) {
      return [];
    }

    const parsed = [];
    
    for (const tool of tools) {
      if (typeof tool === 'string') {
        // 支持逗号分隔和空格分隔
        const items = tool.split(/[,\s]+/).filter(item => item.trim());
        parsed.push(...items);
      }
    }

    return parsed;
  }

  /**
   * 解析环境变量
   * @param {Array} envVars - 环境变量数组
   * @returns {Object} 环境变量对象
   */
  parseEnvironmentVariables(envVars) {
    const env = {};
    
    if (!envVars || !Array.isArray(envVars)) {
      return env;
    }

    for (const envVar of envVars) {
      const [key, ...valueParts] = envVar.split('=');
      if (key && valueParts.length > 0) {
        env[key] = valueParts.join('=');
      }
    }

    return env;
  }

  /**
   * 解析HTTP头
   * @param {Array} headers - HTTP头数组
   * @returns {Object} HTTP头对象
   */
  parseHTTPHeaders(headers) {
    const parsed = {};
    
    if (!headers || !Array.isArray(headers)) {
      return parsed;
    }

    for (const header of headers) {
      const [key, ...valueParts] = header.split(':');
      if (key && valueParts.length > 0) {
        parsed[key.trim()] = valueParts.join(':').trim();
      }
    }

    return parsed;
  }

  /**
   * 解析JSON配置
   * @param {string} jsonString - JSON字符串
   * @returns {Object} 解析后的对象
   */
  parseJSONConfig(jsonString) {
    try {
      return JSON.parse(jsonString);
    } catch (error) {
      throw new Error(`Invalid JSON configuration: ${error.message}`);
    }
  }

  /**
   * 验证UUID格式
   * @param {string} uuid - UUID字符串
   * @returns {boolean} 是否为有效UUID
   */
  validateUUID(uuid) {
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    return uuidRegex.test(uuid);
  }
}

/**
 * 全局CLI程序实例
 */
export const globalCLIProgram = new CLIProgram();

/**
 * 全局参数解析器实例
 */
export const globalArgumentParser = new ArgumentParser();

/**
 * 便捷函数：创建CLI程序
 * @returns {Object} Commander程序实例
 */
export function createCLIProgram() {
  return globalCLIProgram.createProgram();
}

/**
 * 便捷函数：解析命令行参数
 * @param {Array} argv - 命令行参数
 * @returns {Promise<Object>} 解析结果
 */
export async function parseCommandLineArguments(argv = process.argv) {
  return globalCLIProgram.parseArguments(argv);
}

/**
 * 便捷函数：解析工具列表
 * @param {Array} tools - 工具列表
 * @returns {Array} 解析后的工具列表
 */
export function parseToolsList(tools) {
  return globalArgumentParser.parseToolsList(tools);
}
