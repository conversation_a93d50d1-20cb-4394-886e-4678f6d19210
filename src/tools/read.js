/**
 * Read工具实现
 * 
 * 提供安全的文件读取功能，支持多种文件格式
 * 
 * @original: Read工具相关代码 (L2954-2968, L3766-3898)
 */

import { readFileSync, statSync } from 'fs';
import { logger } from '../utils/logger.js';
import { safeReadFile, checkFileAccess, formatFileContent } from '../utils/filesystem.js';
import { readPDFFile, isPDFFile } from '../services/pdf.js';

/**
 * Read工具名称
 * @original: aJ变量
 */
export const READ_TOOL_NAME = 'Read';

/**
 * 最大行长度
 * @original: t6Q变量 (L2952)
 */
export const MAX_LINE_LENGTH = 2000;

/**
 * 支持的图片扩展名
 */
export const IMAGE_EXTENSIONS = new Set([
  'png', 'jpg', 'jpeg', 'gif', 'bmp', 'webp', 'svg', 'ico'
]);

/**
 * 支持的文本文件扩展名
 */
export const TEXT_EXTENSIONS = new Set([
  'txt', 'md', 'js', 'ts', 'jsx', 'tsx', 'py', 'java', 'cpp', 'c', 'h',
  'css', 'scss', 'sass', 'html', 'xml', 'json', 'yaml', 'yml', 'toml',
  'ini', 'cfg', 'conf', 'log', 'sql', 'sh', 'bash', 'zsh', 'fish',
  'ps1', 'bat', 'cmd', 'dockerfile', 'makefile', 'cmake', 'gradle',
  'properties', 'env', 'gitignore', 'gitattributes', 'editorconfig'
]);

/**
 * Read工具类
 */
export class ReadTool {
  constructor(options = {}) {
    this.name = READ_TOOL_NAME;
    this.maxLineLength = options.maxLineLength || MAX_LINE_LENGTH;
    this.supportPDF = options.supportPDF !== false;
    this.supportImages = options.supportImages !== false;
  }

  /**
   * 工具描述
   * @original: XIA和VIA变量 (L2953-2968)
   * @returns {string} 工具描述
   */
  async description() {
    const pdfSupport = this.supportPDF 
      ? '\n- This tool can read PDF files (.pdf). PDFs are processed page by page, extracting both text and visual content for analysis.'
      : '';

    return `Reads a file from the local filesystem. You can access any file directly by using this tool.
Assume this tool is able to read all files on the machine. If the User provides a path to a file assume that path is valid. It is okay to read a file that does not exist; an error will be returned.

Usage:
- The file_path parameter must be an absolute path, not a relative path
- If the file is a text file, the contents will be returned as text
- Binary files will be described rather than having their contents returned
- Any lines longer than ${this.maxLineLength} characters will be truncated
- Results are returned using cat -n format, with line numbers starting at 1
- This tool allows Claude Code to read images (eg PNG, JPG, etc). When reading an image file the contents are presented visually as Claude Code is a multimodal LLM.${pdfSupport}
- This tool can read Jupyter notebooks (.ipynb files) and returns all cells with their outputs, combining code, text, and visualizations.
- You have the capability to call multiple tools in a single response. It is always better to speculatively read multiple files as a batch that are potentially useful.
- You will regularly be asked to read screenshots. If the user provides a path to a screenshot ALWAYS use this tool to view the file at the path.
- If you read a file that exists but has empty contents you will receive a system reminder warning in place of file contents.`;
  }

  /**
   * 验证输入
   * @param {Object} input - 输入参数
   * @param {Object} context - 验证上下文
   * @returns {Promise<Object>} 验证结果
   */
  async validateInput(input, context) {
    if (!input.file_path || typeof input.file_path !== 'string') {
      return {
        result: false,
        message: 'file_path is required and must be a string'
      };
    }

    // 检查路径是否为绝对路径
    if (!input.file_path.startsWith('/') && !input.file_path.match(/^[A-Za-z]:\\/)) {
      return {
        result: false,
        message: 'file_path must be an absolute path, not a relative path'
      };
    }

    return { result: true };
  }

  /**
   * 读取文件
   * @param {Object} input - 输入参数
   * @param {Object} context - 执行上下文
   * @returns {Promise<Object>} 读取结果
   */
  async call(input, context) {
    const { file_path } = input;
    
    logger.debug('读取文件', { filePath: file_path });

    try {
      // 检查文件访问权限
      const accessCheck = checkFileAccess(file_path);
      if (!accessCheck.exists) {
        return {
          type: 'error',
          message: `File not found: ${file_path}`
        };
      }

      if (!accessCheck.readable) {
        return {
          type: 'error',
          message: `File not readable: ${file_path}`
        };
      }

      if (accessCheck.isDirectory) {
        return {
          type: 'error',
          message: `Path is a directory, not a file: ${file_path}`
        };
      }

      // 检查文件类型并相应处理
      const fileType = this.detectFileType(file_path);
      
      switch (fileType) {
        case 'pdf':
          return await this.readPDFFile(file_path);
        case 'image':
          return await this.readImageFile(file_path);
        case 'jupyter':
          return await this.readJupyterNotebook(file_path);
        case 'binary':
          return await this.readBinaryFile(file_path);
        default:
          return await this.readTextFile(file_path);
      }

    } catch (error) {
      logger.error('文件读取失败', { filePath: file_path, error: error.message });
      
      return {
        type: 'error',
        message: `Failed to read file: ${error.message}`
      };
    }
  }

  /**
   * 检测文件类型
   * @param {string} filePath - 文件路径
   * @returns {string} 文件类型
   */
  detectFileType(filePath) {
    const extension = filePath.toLowerCase().split('.').pop();
    
    if (extension === 'pdf' && this.supportPDF) {
      return 'pdf';
    }
    
    if (IMAGE_EXTENSIONS.has(extension) && this.supportImages) {
      return 'image';
    }
    
    if (extension === 'ipynb') {
      return 'jupyter';
    }
    
    if (TEXT_EXTENSIONS.has(extension)) {
      return 'text';
    }
    
    // 检查是否为二进制文件
    try {
      const stats = statSync(filePath);
      if (stats.size > 1024 * 1024) { // 大于1MB的文件可能是二进制
        return 'binary';
      }
      
      // 尝试读取前1024字节检查是否包含二进制字符
      const buffer = readFileSync(filePath, { encoding: null, flag: 'r' });
      const sample = buffer.slice(0, Math.min(1024, buffer.length));
      
      for (let i = 0; i < sample.length; i++) {
        const byte = sample[i];
        if (byte === 0 || (byte < 32 && byte !== 9 && byte !== 10 && byte !== 13)) {
          return 'binary';
        }
      }
      
      return 'text';
      
    } catch (error) {
      return 'text'; // 默认当作文本处理
    }
  }

  /**
   * 读取文本文件
   * @param {string} filePath - 文件路径
   * @returns {Promise<Object>} 读取结果
   */
  async readTextFile(filePath) {
    const readResult = safeReadFile(filePath);
    
    if (!readResult.success) {
      return {
        type: 'error',
        message: readResult.error
      };
    }

    let content = readResult.content;
    
    // 检查空文件
    if (content.length === 0) {
      return {
        type: 'warning',
        message: `File exists but is empty: ${filePath}`,
        content: ''
      };
    }

    // 处理长行截断
    const lines = content.split('\n');
    const processedLines = lines.map(line => {
      if (line.length > this.maxLineLength) {
        return line.substring(0, this.maxLineLength) + '... (line truncated)';
      }
      return line;
    });

    // 格式化输出（添加行号）
    const formattedContent = formatFileContent({
      content: processedLines.join('\n'),
      startLine: 1
    });

    return {
      type: 'text',
      content: formattedContent,
      stats: readResult.stats
    };
  }

  /**
   * 读取PDF文件
   * @param {string} filePath - 文件路径
   * @returns {Promise<Object>} 读取结果
   */
  async readPDFFile(filePath) {
    if (!this.supportPDF) {
      return {
        type: 'error',
        message: 'PDF reading is not supported'
      };
    }

    try {
      const pdfInfo = await readPDFFile(filePath);
      
      return {
        type: 'pdf',
        content: `PDF file read: ${filePath} (${pdfInfo.getFormattedSize()})`,
        pdfData: pdfInfo.toClaudeFormat()
      };

    } catch (error) {
      return {
        type: 'error',
        message: `Failed to read PDF: ${error.message}`
      };
    }
  }

  /**
   * 读取图片文件
   * @param {string} filePath - 文件路径
   * @returns {Promise<Object>} 读取结果
   */
  async readImageFile(filePath) {
    if (!this.supportImages) {
      return {
        type: 'error',
        message: 'Image reading is not supported'
      };
    }

    try {
      const buffer = readFileSync(filePath);
      const base64Data = buffer.toString('base64');
      const extension = filePath.toLowerCase().split('.').pop();
      
      const mimeType = {
        'png': 'image/png',
        'jpg': 'image/jpeg',
        'jpeg': 'image/jpeg',
        'gif': 'image/gif',
        'bmp': 'image/bmp',
        'webp': 'image/webp',
        'svg': 'image/svg+xml'
      }[extension] || 'image/png';

      return {
        type: 'image',
        content: `Image file read: ${filePath} (${buffer.length} bytes)`,
        imageData: {
          type: 'image',
          source: {
            type: 'base64',
            media_type: mimeType,
            data: base64Data
          }
        }
      };

    } catch (error) {
      return {
        type: 'error',
        message: `Failed to read image: ${error.message}`
      };
    }
  }

  /**
   * 读取Jupyter笔记本
   * @param {string} filePath - 文件路径
   * @returns {Promise<Object>} 读取结果
   */
  async readJupyterNotebook(filePath) {
    try {
      const readResult = safeReadFile(filePath);
      
      if (!readResult.success) {
        return {
          type: 'error',
          message: readResult.error
        };
      }

      const notebook = JSON.parse(readResult.content);
      
      // 处理笔记本内容
      const processedContent = this.processJupyterCells(notebook.cells || []);

      return {
        type: 'jupyter',
        content: `Jupyter notebook read: ${filePath}\n\n${processedContent}`,
        metadata: notebook.metadata
      };

    } catch (error) {
      return {
        type: 'error',
        message: `Failed to read Jupyter notebook: ${error.message}`
      };
    }
  }

  /**
   * 处理Jupyter单元格
   * @param {Array} cells - 单元格数组
   * @returns {string} 处理后的内容
   */
  processJupyterCells(cells) {
    return cells.map((cell, index) => {
      const cellNumber = index + 1;
      let content = `Cell ${cellNumber} (${cell.cell_type}):\n`;
      
      if (cell.source) {
        const source = Array.isArray(cell.source) 
          ? cell.source.join('') 
          : cell.source;
        content += source + '\n';
      }
      
      if (cell.outputs && cell.outputs.length > 0) {
        content += 'Output:\n';
        for (const output of cell.outputs) {
          if (output.text) {
            const text = Array.isArray(output.text) 
              ? output.text.join('') 
              : output.text;
            content += text + '\n';
          }
        }
      }
      
      return content;
    }).join('\n---\n\n');
  }

  /**
   * 读取二进制文件
   * @param {string} filePath - 文件路径
   * @returns {Promise<Object>} 读取结果
   */
  async readBinaryFile(filePath) {
    try {
      const stats = statSync(filePath);
      const extension = filePath.toLowerCase().split('.').pop();
      
      return {
        type: 'binary',
        content: `Binary file: ${filePath} (${this.formatFileSize(stats.size)}, .${extension} format)\n` +
                `This is a binary file and cannot be displayed as text.`,
        stats: {
          size: stats.size,
          extension,
          mtime: stats.mtime
        }
      };

    } catch (error) {
      return {
        type: 'error',
        message: `Failed to read binary file: ${error.message}`
      };
    }
  }

  /**
   * 格式化文件大小
   * @param {number} bytes - 字节数
   * @returns {string} 格式化的文件大小
   */
  formatFileSize(bytes) {
    if (bytes === 0) return '0 bytes';
    
    const units = ['bytes', 'KB', 'MB', 'GB'];
    const k = 1024;
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return `${(bytes / Math.pow(k, i)).toFixed(1)} ${units[i]}`;
  }

  /**
   * 检查工具是否启用
   * @returns {boolean} 是否启用
   */
  isEnabled() {
    return true;
  }

  /**
   * 获取输入模式
   * @returns {Object} 输入模式定义
   */
  get inputSchema() {
    return {
      type: 'object',
      properties: {
        file_path: {
          type: 'string',
          description: 'The absolute path to the file to read'
        }
      },
      required: ['file_path']
    };
  }

  /**
   * 渲染工具使用消息
   * @param {Object} input - 输入参数
   * @param {Object} options - 渲染选项
   * @returns {JSX.Element} 渲染结果
   */
  renderToolUseMessage(input, options) {
    const { verbose } = options;
    
    if (verbose) {
      return `Reading file: ${input.file_path}`;
    }
    
    return `📄 ${input.file_path}`;
  }

  /**
   * 渲染工具结果消息
   * @param {Object} result - 工具结果
   * @param {Object} input - 输入参数
   * @param {Object} options - 渲染选项
   * @returns {string} 渲染结果
   */
  renderToolResultMessage(result, input, options) {
    const { verbose } = options;
    
    if (result.type === 'error') {
      return `❌ ${result.message}`;
    }
    
    if (result.type === 'warning') {
      return `⚠️ ${result.message}`;
    }
    
    if (verbose) {
      return `File read successfully: ${input.file_path}`;
    }
    
    return `✅ Read ${input.file_path}`;
  }

  /**
   * 映射工具结果到工具结果块参数
   * @param {Object} result - 工具结果
   * @param {string} toolUseId - 工具使用ID
   * @returns {Object} 工具结果块参数
   */
  mapToolResultToToolResultBlockParam(result, toolUseId) {
    return {
      tool_use_id: toolUseId,
      type: 'tool_result',
      content: result.content || result.message
    };
  }
}

/**
 * 创建Read工具实例
 * @param {Object} options - 配置选项
 * @returns {ReadTool} Read工具实例
 */
export function createReadTool(options = {}) {
  return new ReadTool(options);
}

/**
 * 默认Read工具实例
 */
export const readTool = createReadTool();

/**
 * 便捷函数：读取文件
 * @param {string} filePath - 文件路径
 * @returns {Promise<Object>} 读取结果
 */
export async function readFile(filePath) {
  return readTool.call({ file_path: filePath }, {
    abortController: new AbortController()
  });
}
