/**
 * 主应用UI组件
 * 
 * Claude Code的主要用户界面组件
 * 
 * @original: 主应用组件 (L53970-54100, L13310-13355)
 */

import React from 'react';
import { Box, Text, SYMBOLS, COLORS, Fragment } from './components/base.js';
import { LoadingSpinner } from './components/base.js';
import { Dialog, WarningDialog, InfoDialog } from './components/dialog.js';
import { Select } from './components/select.js';
import { AppStateProvider, useAppState, useMessages, useTools } from '../core/state.js';
import { logger } from '../utils/logger.js';

/**
 * 主应用组件
 * @original: ZA1组件 (L53970)
 * @param {Object} props - 组件属性
 * @returns {JSX.Element} 主应用组件
 */
export function ClaudeCodeApp({
  initialState,
  onChangeAppState,
  debug = false,
  initialPrompt = '',
  commands = [],
  tools = [],
  clients = [],
  verbose = false,
  ...props
}) {
  return React.createElement(AppStateProvider, {
    initialState,
    onStateChange: onChangeAppState
  },
    React.createElement(MainAppContent, {
      debug,
      initialPrompt,
      commands,
      tools,
      clients,
      verbose,
      ...props
    })
  );
}

/**
 * 主应用内容组件
 * @original: 主应用内容逻辑 (L53681-54100)
 */
function MainAppContent({
  debug,
  initialPrompt,
  commands,
  tools,
  clients,
  verbose,
  mode = 'interactive' // 'interactive' | 'transcript'
}) {
  const { state, updateState } = useAppState();
  const { messages } = useMessages();
  const { toolJSX, toolUseConfirmQueue } = useTools();

  // 状态变量
  const [currentMode, setCurrentMode] = React.useState(mode);
  const [isLoading, setIsLoading] = React.useState(false);
  const [error, setError] = React.useState(null);

  // 初始化效果
  React.useEffect(() => {
    logger.debug('主应用组件初始化', { debug, verbose, mode });
    
    // 初始化应用状态
    updateState({
      isInitialized: true,
      mode: currentMode
    });
  }, []);

  // 处理模式切换
  const handleModeToggle = React.useCallback(() => {
    const newMode = currentMode === 'interactive' ? 'transcript' : 'interactive';
    setCurrentMode(newMode);
    updateState({ currentView: newMode });
  }, [currentMode, updateState]);

  // 渲染转录模式
  if (currentMode === 'transcript') {
    return React.createElement(TranscriptView, {
      messages,
      tools,
      verbose: true,
      toolJSX: null,
      toolUseConfirmQueue: [],
      onModeToggle: handleModeToggle
    });
  }

  // 渲染交互模式
  return React.createElement(Fragment, null,
    // 主要内容区域
    React.createElement(MessageList, {
      messages,
      tools,
      verbose,
      toolJSX,
      toolUseConfirmQueue,
      debug
    }),
    
    // 工具JSX区域
    toolJSX && React.createElement(Box, {
      paddingX: 2,
      paddingTop: 1
    }, toolJSX.jsx),
    
    // 输入区域
    React.createElement(InputArea, {
      debug,
      verbose,
      commands,
      tools,
      onModeToggle: handleModeToggle
    }),
    
    // 对话框和模态框
    React.createElement(DialogLayer, {
      state
    })
  );
}

/**
 * 欢迎界面组件
 * @original: 欢迎界面逻辑 (L13310-13355)
 * @param {Object} props - 组件属性
 * @returns {JSX.Element} 欢迎界面组件
 */
export function WelcomeScreen({
  ideName = 'VS Code',
  version = '1.0.72',
  installMethod = 'native',
  needsRestart = false,
  onContinue,
  ...props
}) {
  const quitKey = process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q';
  const referenceKey = process.platform === 'darwin' ? 'Cmd+Option+K' : 'Ctrl+Alt+K';

  return React.createElement(Fragment, null,
    React.createElement(Box, {
      flexDirection: 'column'
    },
      // 主要欢迎信息
      React.createElement(Box, {
        flexDirection: 'column',
        borderStyle: 'round',
        borderColor: COLORS.ide,
        paddingLeft: 1,
        paddingRight: 1,
        gap: 1
      },
        React.createElement(Box, null,
          React.createElement(Text, {
            color: COLORS.claude
          }, '✻ '),
          React.createElement(Box, {
            flexDirection: 'column'
          },
            React.createElement(Text, null,
              'Welcome to ',
              React.createElement(Text, { bold: true }, 'Claude Code'),
              ' for ',
              React.createElement(Text, {
                color: COLORS.ide,
                bold: true
              }, ideName)
            ),
            React.createElement(Text, {
              color: COLORS.secondaryText
            }, `installed ${installMethod} v${version}`)
          )
        ),
        
        // 重启警告
        needsRestart && React.createElement(Box, {
          marginTop: 1
        },
          React.createElement(Text, {
            color: COLORS.warning
          }, `${SYMBOLS.warning} Restart ${ideName} (${quitKey}) to continue (may require multiple restarts)`)
        ),
        
        // 功能介绍
        React.createElement(Box, {
          flexDirection: 'column',
          paddingLeft: 1,
          gap: 1
        },
          React.createElement(Text, null,
            '• Claude has context of ',
            React.createElement(Text, { color: COLORS.suggestion }, '⧉ open files'),
            ' and ',
            React.createElement(Text, { color: COLORS.suggestion }, '⧉ selected lines')
          ),
          React.createElement(Text, null,
            '• Review Claude Code\'s changes ',
            React.createElement(Text, { color: COLORS.diffAddedWord }, '+11'),
            ' ',
            React.createElement(Text, { color: COLORS.diffRemovedWord }, '-22'),
            ' in the comfort of your IDE'
          ),
          React.createElement(Text, null,
            '• Cmd+Esc',
            React.createElement(Text, { color: COLORS.secondaryText }, ' for Quick Launch')
          ),
          React.createElement(Text, null,
            '• ',
            referenceKey,
            React.createElement(Text, { color: COLORS.secondaryText }, ' to reference files or lines in your input')
          )
        )
      )
    ),
    
    // 继续提示
    React.createElement(Box, {
      marginLeft: 3
    },
      React.createElement(ContinuePrompt, {
        onContinue
      })
    )
  );
}

/**
 * 继续提示组件
 * @param {Object} props - 组件属性
 * @returns {JSX.Element} 继续提示组件
 */
function ContinuePrompt({ onContinue }) {
  const [keyState, setKeyState] = React.useState({ pending: false, keyName: null });

  React.useEffect(() => {
    const handleKeyPress = (key) => {
      if (key === 'return') {
        onContinue?.();
      }
    };

    // 这里应该绑定到实际的键盘事件处理器
    
    return () => {
      // 清理事件监听器
    };
  }, [onContinue]);

  return React.createElement(Text, {
    dimColor: true
  },
    keyState.pending 
      ? `Press ${keyState.keyName} again to exit`
      : 'Press Enter to continue'
  );
}

/**
 * 消息列表组件
 * @param {Object} props - 组件属性
 * @returns {JSX.Element} 消息列表组件
 */
function MessageList({
  messages,
  tools,
  verbose,
  toolJSX,
  toolUseConfirmQueue,
  debug
}) {
  // 实现消息列表渲染逻辑
  return React.createElement(Box, {
    flexDirection: 'column',
    flexGrow: 1
  },
    messages.map((message, index) => 
      React.createElement(MessageItem, {
        key: message.id || index,
        message,
        verbose,
        debug
      })
    )
  );
}

/**
 * 消息项组件
 * @param {Object} props - 组件属性
 * @returns {JSX.Element} 消息项组件
 */
function MessageItem({ message, verbose, debug }) {
  return React.createElement(Box, {
    flexDirection: 'column',
    marginBottom: 1
  },
    React.createElement(Text, {
      color: message.role === 'user' ? COLORS.primary : COLORS.claude
    }, `${message.role}: ${message.content}`)
  );
}

/**
 * 转录视图组件
 * @param {Object} props - 组件属性
 * @returns {JSX.Element} 转录视图组件
 */
function TranscriptView({
  messages,
  tools,
  verbose,
  toolJSX,
  toolUseConfirmQueue,
  onModeToggle
}) {
  return React.createElement(Fragment, null,
    React.createElement(MessageList, {
      messages,
      tools,
      verbose: true,
      toolJSX: null,
      toolUseConfirmQueue: []
    }),
    
    React.createElement(Box, {
      alignItems: 'center',
      alignSelf: 'center',
      borderTopColor: COLORS.secondaryBorder,
      borderTopStyle: 'single',
      justifyContent: 'center',
      marginTop: 1,
      paddingTop: 1,
      paddingLeft: 2,
      width: '100%'
    },
      React.createElement(Text, {
        dimColor: true
      }, 'Showing detailed transcript · Ctrl+R to toggle')
    )
  );
}

/**
 * 输入区域组件
 * @param {Object} props - 组件属性
 * @returns {JSX.Element} 输入区域组件
 */
function InputArea({
  debug,
  verbose,
  commands,
  tools,
  onModeToggle
}) {
  // 实现输入区域逻辑
  return React.createElement(Box, {
    flexDirection: 'column',
    width: '100%'
  },
    React.createElement(Text, {
      color: COLORS.secondaryText
    }, 'Type your message...')
  );
}

/**
 * 对话框层组件
 * @param {Object} props - 组件属性
 * @returns {JSX.Element} 对话框层组件
 */
function DialogLayer({ state }) {
  // 根据状态显示相应的对话框
  if (state.showCostWarning) {
    return React.createElement(CostWarningDialog, {
      onDone: () => {
        // 处理成本警告确认
      }
    });
  }

  if (state.showInstallationDialog) {
    return React.createElement(InstallationDialog, {
      status: state.installationStatus,
      onDone: () => {
        // 处理安装对话框关闭
      }
    });
  }

  return null;
}

/**
 * 成本警告对话框
 * @param {Object} props - 组件属性
 * @returns {JSX.Element} 成本警告对话框组件
 */
function CostWarningDialog({ onDone }) {
  return React.createElement(WarningDialog, {
    title: 'API Cost Warning',
    message: "You've spent $5 on the Anthropic API this session.",
    details: 'Learn more about how to monitor your spending:',
    acceptLabel: 'Got it, thanks!',
    onAccept: onDone,
    onDecline: onDone
  });
}

/**
 * 安装对话框
 * @param {Object} props - 组件属性
 * @returns {JSX.Element} 安装对话框组件
 */
function InstallationDialog({ status, onDone }) {
  const getStatusMessage = () => {
    switch (status?.type) {
      case 'checking':
        return 'Checking installation status...';
      case 'installing':
        return `Installing Claude Code ${status.version}...`;
      case 'success':
        return 'Installation completed successfully!';
      case 'error':
        return `Installation failed: ${status.message}`;
      default:
        return 'Processing...';
    }
  };

  const getStatusColor = () => {
    switch (status?.type) {
      case 'success':
        return COLORS.success;
      case 'error':
        return COLORS.error;
      default:
        return COLORS.info;
    }
  };

  return React.createElement(InfoDialog, {
    title: 'Installation',
    message: getStatusMessage(),
    onClose: onDone,
    borderColor: getStatusColor()
  });
}

/**
 * 应用包装器组件
 * @original: G7组件 (L8484)
 * @param {Object} props - 组件属性
 * @returns {JSX.Element} 应用包装器组件
 */
export function AppWrapper({
  children,
  initialState,
  onChangeAppState,
  theme = 'dark',
  ...props
}) {
  return React.createElement(AppStateProvider, {
    initialState,
    onStateChange: onChangeAppState
  },
    React.createElement(ThemeProvider, {
      theme
    },
      React.createElement(Box, {
        flexDirection: 'column',
        height: '100%',
        ...props
      }, children)
    )
  );
}

/**
 * 主题Provider组件
 * @param {Object} props - 组件属性
 * @returns {JSX.Element} 主题Provider组件
 */
function ThemeProvider({ children, theme = 'dark' }) {
  const themeValue = React.useMemo(() => ({
    name: theme,
    colors: theme === 'dark' ? DARK_THEME_COLORS : LIGHT_THEME_COLORS
  }), [theme]);

  return React.createElement(ThemeContext.Provider, {
    value: themeValue
  }, children);
}

/**
 * 主题上下文
 */
const ThemeContext = React.createContext({
  name: 'dark',
  colors: COLORS
});

/**
 * 使用主题Hook
 * @returns {Object} 主题对象
 */
export function useTheme() {
  return React.useContext(ThemeContext);
}

/**
 * 深色主题颜色
 */
const DARK_THEME_COLORS = {
  ...COLORS,
  background: 'black',
  text: 'white',
  primary: 'cyan',
  secondary: 'gray'
};

/**
 * 浅色主题颜色
 */
const LIGHT_THEME_COLORS = {
  ...COLORS,
  background: 'white',
  text: 'black',
  primary: 'blue',
  secondary: 'gray'
};

/**
 * 错误边界组件
 * @param {Object} props - 组件属性
 * @returns {JSX.Element} 错误边界组件
 */
export class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true, error };
  }

  componentDidCatch(error, errorInfo) {
    logger.error('React错误边界捕获错误', { error, errorInfo });
  }

  render() {
    if (this.state.hasError) {
      return React.createElement(Dialog, {
        title: 'Application Error',
        borderColor: COLORS.error
      },
        React.createElement(Text, {
          color: COLORS.error
        }, 'An unexpected error occurred:'),
        React.createElement(Text, {
          color: COLORS.secondaryText
        }, this.state.error?.message || 'Unknown error'),
        React.createElement(Text, {
          color: COLORS.secondaryText
        }, 'Please restart the application.')
      );
    }

    return this.props.children;
  }
}

/**
 * 应用根组件
 * @param {Object} props - 组件属性
 * @returns {JSX.Element} 应用根组件
 */
export function AppRoot(props) {
  return React.createElement(ErrorBoundary, null,
    React.createElement(ClaudeCodeApp, props)
  );
}
