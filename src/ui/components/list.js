/**
 * 列表UI组件
 * 
 * 提供有序列表、无序列表等列表组件
 * 
 * @original: 列表相关组件 (L7437-7446, L7950-7957, L7977-7991)
 */

import React from 'react';
import { Box, Text, SYMBOLS, COLORS } from './base.js';
import { logger } from '../../utils/logger.js';

/**
 * 列表项上下文
 */
const ListItemContext = React.createContext({
  marker: '•'
});

/**
 * 列表项组件
 * @original: 列表项组件逻辑 (L7437)
 * @param {Object} props - 组件属性
 * @returns {JSX.Element} 列表项组件
 */
export function ListItem({
  children,
  marker,
  color = COLORS.primary,
  markerColor = COLORS.secondaryText,
  ...props
}) {
  const context = React.useContext(ListItemContext);
  const displayMarker = marker || context.marker;

  return React.createElement(Box, {
    flexDirection: 'row',
    ...props
  },
    // 标记
    React.createElement(Text, {
      color: markerColor
    }, displayMarker),
    
    // 内容
    React.createElement(Box, {
      flexDirection: 'column',
      marginLeft: 1
    }, children)
  );
}

/**
 * 无序列表组件
 * @original: UnorderedList组件逻辑 (L7437-7446)
 * @param {Object} props - 组件属性
 * @returns {JSX.Element} 无序列表组件
 */
export function UnorderedList({
  children,
  marker = SYMBOLS.bullet,
  gap = 0,
  ...props
}) {
  const contextValue = React.useMemo(() => ({
    marker
  }), [marker]);

  return React.createElement(ListItemContext.Provider, {
    value: contextValue
  },
    React.createElement(Box, {
      flexDirection: 'column',
      gap,
      ...props
    }, children)
  );
}

/**
 * 有序列表组件
 * @original: OrderedList组件逻辑 (L7950-7957, L7977-7991)
 * @param {Object} props - 组件属性
 * @returns {JSX.Element} 有序列表组件
 */
export function OrderedList({
  children,
  start = 1,
  separator = '.',
  gap = 0,
  ...props
}) {
  // 计算最大数字长度用于对齐
  const childrenArray = React.Children.toArray(children);
  const maxNumber = start + childrenArray.length - 1;
  const maxLength = String(maxNumber).length;

  return React.createElement(Box, {
    flexDirection: 'column',
    gap,
    ...props
  },
    React.Children.map(children, (child, index) => {
      if (!React.isValidElement(child) || child.type !== ListItem) {
        return child;
      }

      const number = start + index;
      const marker = `${String(number).padStart(maxLength)}${separator}`;

      return React.createElement(ListItemContext.Provider, {
        value: { marker }
      }, child);
    })
  );
}

/**
 * 描述列表组件
 * @param {Object} props - 组件属性
 * @returns {JSX.Element} 描述列表组件
 */
export function DescriptionList({
  children,
  gap = 1,
  termColor = COLORS.primary,
  descriptionColor,
  ...props
}) {
  return React.createElement(Box, {
    flexDirection: 'column',
    gap,
    ...props
  }, children);
}

/**
 * 描述列表项组件
 * @param {Object} props - 组件属性
 * @returns {JSX.Element} 描述列表项组件
 */
export function DescriptionItem({
  term,
  description,
  termColor = COLORS.primary,
  descriptionColor,
  layout = 'vertical', // 'vertical' | 'horizontal'
  termWidth = 20,
  ...props
}) {
  if (layout === 'horizontal') {
    return React.createElement(Box, {
      flexDirection: 'row',
      ...props
    },
      React.createElement(Box, {
        width: termWidth,
        flexShrink: 0
      },
        React.createElement(Text, {
          bold: true,
          color: termColor
        }, term)
      ),
      React.createElement(Box, {
        flexGrow: 1
      },
        React.createElement(Text, {
          color: descriptionColor
        }, description)
      )
    );
  }

  return React.createElement(Box, {
    flexDirection: 'column',
    ...props
  },
    React.createElement(Text, {
      bold: true,
      color: termColor
    }, term),
    React.createElement(Text, {
      color: descriptionColor,
      marginLeft: 2
    }, description)
  );
}

/**
 * 可滚动列表组件
 * @param {Object} props - 组件属性
 * @returns {JSX.Element} 可滚动列表组件
 */
export function ScrollableList({
  items = [],
  renderItem,
  keyExtractor = (item, index) => index,
  visibleCount = 10,
  selectedIndex = 0,
  onSelectionChange,
  gap = 0,
  showScrollIndicators = true,
  ...props
}) {
  const [scrollOffset, setScrollOffset] = React.useState(0);

  // 计算可见项目
  const visibleItems = React.useMemo(() => {
    const startIndex = Math.max(0, selectedIndex - Math.floor(visibleCount / 2));
    const endIndex = Math.min(items.length, startIndex + visibleCount);
    const adjustedStartIndex = Math.max(0, endIndex - visibleCount);
    
    return items.slice(adjustedStartIndex, endIndex).map((item, index) => ({
      item,
      originalIndex: adjustedStartIndex + index,
      isSelected: adjustedStartIndex + index === selectedIndex
    }));
  }, [items, selectedIndex, visibleCount]);

  // 处理键盘导航
  React.useEffect(() => {
    const handleKeyPress = (key) => {
      if (key === 'up' || key === 'upArrow') {
        const newIndex = Math.max(0, selectedIndex - 1);
        onSelectionChange?.(newIndex);
      } else if (key === 'down' || key === 'downArrow') {
        const newIndex = Math.min(items.length - 1, selectedIndex + 1);
        onSelectionChange?.(newIndex);
      }
    };

    // 这里应该绑定到实际的键盘事件处理器
    
    return () => {
      // 清理事件监听器
    };
  }, [selectedIndex, items.length, onSelectionChange]);

  const canScrollUp = selectedIndex > 0;
  const canScrollDown = selectedIndex < items.length - 1;

  return React.createElement(Box, {
    flexDirection: 'column',
    ...props
  },
    // 向上滚动指示器
    showScrollIndicators && canScrollUp && React.createElement(Text, {
      color: COLORS.secondaryText,
      alignSelf: 'center'
    }, SYMBOLS.arrowUp),
    
    // 可见项目列表
    React.createElement(Box, {
      flexDirection: 'column',
      gap
    },
      visibleItems.map(({ item, originalIndex, isSelected }) => {
        const key = keyExtractor(item, originalIndex);
        const renderedItem = renderItem ? renderItem(item, originalIndex, isSelected) : item;
        
        return React.createElement(Box, {
          key,
          backgroundColor: isSelected ? COLORS.primary : undefined
        }, renderedItem);
      })
    ),
    
    // 向下滚动指示器
    showScrollIndicators && canScrollDown && React.createElement(Text, {
      color: COLORS.secondaryText,
      alignSelf: 'center'
    }, SYMBOLS.arrowDown)
  );
}

/**
 * 树形列表组件
 * @param {Object} props - 组件属性
 * @returns {JSX.Element} 树形列表组件
 */
export function TreeList({
  items = [],
  renderItem,
  getChildren = (item) => item.children,
  keyExtractor = (item, index) => index,
  expandedKeys = new Set(),
  onToggleExpand,
  indentSize = 2,
  expandIcon = SYMBOLS.arrowRight,
  collapseIcon = SYMBOLS.arrowDown,
  leafIcon = SYMBOLS.bullet,
  ...props
}) {
  const renderTreeItem = React.useCallback((item, index, depth = 0) => {
    const key = keyExtractor(item, index);
    const children = getChildren(item);
    const hasChildren = children && children.length > 0;
    const isExpanded = expandedKeys.has(key);
    
    const indent = ' '.repeat(depth * indentSize);
    let icon = leafIcon;
    
    if (hasChildren) {
      icon = isExpanded ? collapseIcon : expandIcon;
    }

    const handleToggle = () => {
      if (hasChildren) {
        onToggleExpand?.(key, !isExpanded);
      }
    };

    const itemElement = React.createElement(Box, {
      key,
      flexDirection: 'row',
      alignItems: 'center'
    },
      React.createElement(Text, {
        color: COLORS.secondaryText
      }, indent),
      
      React.createElement(Text, {
        color: hasChildren ? COLORS.primary : COLORS.secondaryText,
        onClick: handleToggle
      }, icon, ' '),
      
      renderItem ? renderItem(item, index, depth) : React.createElement(Text, null, String(item))
    );

    const result = [itemElement];

    // 渲染子项目
    if (hasChildren && isExpanded) {
      children.forEach((child, childIndex) => {
        result.push(...renderTreeItem(child, childIndex, depth + 1));
      });
    }

    return result;
  }, [keyExtractor, getChildren, expandedKeys, onToggleExpand, indentSize, expandIcon, collapseIcon, leafIcon, renderItem]);

  const renderedItems = React.useMemo(() => {
    const result = [];
    items.forEach((item, index) => {
      result.push(...renderTreeItem(item, index));
    });
    return result;
  }, [items, renderTreeItem]);

  return React.createElement(Box, {
    flexDirection: 'column',
    ...props
  }, renderedItems);
}

/**
 * 分页列表组件
 * @param {Object} props - 组件属性
 * @returns {JSX.Element} 分页列表组件
 */
export function PaginatedList({
  items = [],
  renderItem,
  keyExtractor = (item, index) => index,
  itemsPerPage = 10,
  currentPage = 0,
  onPageChange,
  showPageInfo = true,
  showNavigation = true,
  gap = 0,
  ...props
}) {
  const totalPages = Math.ceil(items.length / itemsPerPage);
  const startIndex = currentPage * itemsPerPage;
  const endIndex = Math.min(startIndex + itemsPerPage, items.length);
  const currentItems = items.slice(startIndex, endIndex);

  const handlePrevPage = React.useCallback(() => {
    if (currentPage > 0) {
      onPageChange?.(currentPage - 1);
    }
  }, [currentPage, onPageChange]);

  const handleNextPage = React.useCallback(() => {
    if (currentPage < totalPages - 1) {
      onPageChange?.(currentPage + 1);
    }
  }, [currentPage, totalPages, onPageChange]);

  return React.createElement(Box, {
    flexDirection: 'column',
    ...props
  },
    // 页面信息
    showPageInfo && React.createElement(Text, {
      color: COLORS.secondaryText,
      alignSelf: 'center'
    }, `Page ${currentPage + 1} of ${totalPages} (${items.length} items)`),
    
    // 当前页项目
    React.createElement(Box, {
      flexDirection: 'column',
      gap,
      marginY: 1
    },
      currentItems.map((item, index) => {
        const key = keyExtractor(item, startIndex + index);
        const renderedItem = renderItem ? renderItem(item, startIndex + index) : item;
        
        return React.createElement(Box, {
          key
        }, renderedItem);
      })
    ),
    
    // 导航控件
    showNavigation && totalPages > 1 && React.createElement(Box, {
      flexDirection: 'row',
      justifyContent: 'center',
      gap: 2
    },
      React.createElement(Text, {
        color: currentPage > 0 ? COLORS.primary : COLORS.secondaryText
      }, currentPage > 0 ? `${SYMBOLS.arrowLeft} Previous` : ''),
      
      React.createElement(Text, {
        color: COLORS.secondaryText
      }, '|'),
      
      React.createElement(Text, {
        color: currentPage < totalPages - 1 ? COLORS.primary : COLORS.secondaryText
      }, currentPage < totalPages - 1 ? `Next ${SYMBOLS.arrowRight}` : '')
    )
  );
}
