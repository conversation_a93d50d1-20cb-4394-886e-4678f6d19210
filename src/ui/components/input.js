/**
 * 输入UI组件
 * 
 * 提供文本输入、密码输入等输入组件
 * 
 * @original: 输入相关组件 (L10944-10953, L10822-10827)
 */

import React from 'react';
import { Box, Text, SYMBOLS, COLORS } from './base.js';
import { logger } from '../../utils/logger.js';

/**
 * 文本输入组件
 * @original: o8组件 (L10944)
 * @param {Object} props - 组件属性
 * @returns {JSX.Element} 文本输入组件
 */
export function TextInput({
  value = '',
  placeholder = '',
  onChange,
  onSubmit,
  onCancel,
  showCursor = true,
  cursorOffset = 0,
  onChangeCursorOffset,
  mask,
  disabled = false,
  color = COLORS.primary,
  backgroundColor,
  borderColor = COLORS.secondaryBorder,
  borderStyle = 'round',
  padding = 1,
  width,
  maxLength,
  ...props
}) {
  const [internalValue, setInternalValue] = React.useState(value);
  const [cursorPosition, setCursorPosition] = React.useState(cursorOffset);

  // 同步外部值
  React.useEffect(() => {
    setInternalValue(value);
  }, [value]);

  // 同步光标位置
  React.useEffect(() => {
    setCursorPosition(cursorOffset);
  }, [cursorOffset]);

  // 处理值变化
  const handleValueChange = React.useCallback((newValue) => {
    if (maxLength && newValue.length > maxLength) {
      return;
    }

    setInternalValue(newValue);
    onChange?.(newValue);
  }, [onChange, maxLength]);

  // 处理光标位置变化
  const handleCursorChange = React.useCallback((newPosition) => {
    setCursorPosition(newPosition);
    onChangeCursorOffset?.(newPosition);
  }, [onChangeCursorOffset]);

  // 处理键盘事件
  React.useEffect(() => {
    if (disabled) return;

    const handleKeyPress = (key, input) => {
      if (input.ctrl && key === 'c') {
        onCancel?.();
        return;
      }

      if (key === 'escape') {
        onCancel?.();
        return;
      }

      if (key === 'return') {
        onSubmit?.(internalValue);
        return;
      }

      if (key === 'backspace') {
        if (cursorPosition > 0) {
          const newValue = internalValue.slice(0, cursorPosition - 1) + 
                          internalValue.slice(cursorPosition);
          handleValueChange(newValue);
          handleCursorChange(cursorPosition - 1);
        }
        return;
      }

      if (key === 'delete') {
        if (cursorPosition < internalValue.length) {
          const newValue = internalValue.slice(0, cursorPosition) + 
                          internalValue.slice(cursorPosition + 1);
          handleValueChange(newValue);
        }
        return;
      }

      if (key === 'left' || key === 'leftArrow') {
        if (cursorPosition > 0) {
          handleCursorChange(cursorPosition - 1);
        }
        return;
      }

      if (key === 'right' || key === 'rightArrow') {
        if (cursorPosition < internalValue.length) {
          handleCursorChange(cursorPosition + 1);
        }
        return;
      }

      if (key === 'home') {
        handleCursorChange(0);
        return;
      }

      if (key === 'end') {
        handleCursorChange(internalValue.length);
        return;
      }

      // 普通字符输入
      if (key.length === 1) {
        const newValue = internalValue.slice(0, cursorPosition) + 
                        key + 
                        internalValue.slice(cursorPosition);
        handleValueChange(newValue);
        handleCursorChange(cursorPosition + 1);
      }
    };

    // 这里应该绑定到实际的键盘事件处理器
    
    return () => {
      // 清理事件监听器
    };
  }, [disabled, internalValue, cursorPosition, handleValueChange, handleCursorChange, onSubmit, onCancel]);

  // 渲染显示值
  const displayValue = React.useMemo(() => {
    let text = internalValue || placeholder;
    
    // 应用掩码
    if (mask && internalValue) {
      text = mask.repeat(internalValue.length);
    }

    // 添加光标
    if (showCursor && !disabled) {
      const beforeCursor = text.slice(0, cursorPosition);
      const afterCursor = text.slice(cursorPosition);
      const cursorChar = cursorPosition < internalValue.length ? text[cursorPosition] : ' ';
      
      return React.createElement(React.Fragment, null,
        beforeCursor,
        React.createElement(Text, {
          backgroundColor: color,
          color: backgroundColor || 'black'
        }, cursorChar),
        afterCursor.slice(1)
      );
    }

    return text;
  }, [internalValue, placeholder, mask, showCursor, disabled, cursorPosition, color, backgroundColor]);

  const inputContent = React.createElement(Text, {
    color: internalValue ? color : COLORS.secondaryText
  }, displayValue);

  if (borderStyle) {
    return React.createElement(Box, {
      borderStyle,
      borderColor,
      borderDimColor: disabled,
      padding,
      width,
      ...props
    }, inputContent);
  }

  return React.createElement(Box, {
    width,
    ...props
  }, inputContent);
}

/**
 * 密码输入组件
 * @param {Object} props - 组件属性
 * @returns {JSX.Element} 密码输入组件
 */
export function PasswordInput({
  mask = '*',
  ...props
}) {
  return React.createElement(TextInput, {
    mask,
    ...props
  });
}

/**
 * 数字输入组件
 * @param {Object} props - 组件属性
 * @returns {JSX.Element} 数字输入组件
 */
export function NumberInput({
  value = '',
  min,
  max,
  step = 1,
  allowFloat = false,
  onChange,
  ...props
}) {
  const handleChange = React.useCallback((newValue) => {
    // 只允许数字字符
    const numericValue = allowFloat 
      ? newValue.replace(/[^0-9.-]/g, '')
      : newValue.replace(/[^0-9-]/g, '');

    // 验证范围
    const numValue = parseFloat(numericValue);
    if (!isNaN(numValue)) {
      if (min !== undefined && numValue < min) return;
      if (max !== undefined && numValue > max) return;
    }

    onChange?.(numericValue);
  }, [onChange, min, max, allowFloat]);

  return React.createElement(TextInput, {
    value,
    onChange: handleChange,
    ...props
  });
}

/**
 * 搜索输入组件
 * @param {Object} props - 组件属性
 * @returns {JSX.Element} 搜索输入组件
 */
export function SearchInput({
  placeholder = 'Search...',
  onSearch,
  debounceMs = 300,
  showSearchIcon = true,
  ...props
}) {
  const [searchValue, setSearchValue] = React.useState('');
  const debounceRef = React.useRef();

  const handleChange = React.useCallback((value) => {
    setSearchValue(value);

    // 防抖搜索
    if (debounceRef.current) {
      clearTimeout(debounceRef.current);
    }

    debounceRef.current = setTimeout(() => {
      onSearch?.(value);
    }, debounceMs);
  }, [onSearch, debounceMs]);

  React.useEffect(() => {
    return () => {
      if (debounceRef.current) {
        clearTimeout(debounceRef.current);
      }
    };
  }, []);

  const inputElement = React.createElement(TextInput, {
    value: searchValue,
    placeholder,
    onChange: handleChange,
    ...props
  });

  if (showSearchIcon) {
    return React.createElement(Box, {
      flexDirection: 'row',
      alignItems: 'center'
    },
      React.createElement(Text, {
        color: COLORS.secondaryText
      }, '🔍 '),
      inputElement
    );
  }

  return inputElement;
}

/**
 * 多行文本输入组件
 * @param {Object} props - 组件属性
 * @returns {JSX.Element} 多行文本输入组件
 */
export function TextArea({
  value = '',
  placeholder = '',
  onChange,
  onSubmit,
  onCancel,
  rows = 3,
  cols = 40,
  wrap = true,
  disabled = false,
  ...props
}) {
  const [internalValue, setInternalValue] = React.useState(value);
  const [cursorRow, setCursorRow] = React.useState(0);
  const [cursorCol, setCursorCol] = React.useState(0);

  // 同步外部值
  React.useEffect(() => {
    setInternalValue(value);
  }, [value]);

  // 处理值变化
  const handleValueChange = React.useCallback((newValue) => {
    setInternalValue(newValue);
    onChange?.(newValue);
  }, [onChange]);

  // 分割文本为行
  const lines = React.useMemo(() => {
    const textLines = internalValue.split('\n');
    
    // 确保至少有指定行数
    while (textLines.length < rows) {
      textLines.push('');
    }

    return textLines.slice(0, rows);
  }, [internalValue, rows]);

  // 处理键盘事件
  React.useEffect(() => {
    if (disabled) return;

    const handleKeyPress = (key, input) => {
      if (input.ctrl && key === 'c') {
        onCancel?.();
        return;
      }

      if (key === 'escape') {
        onCancel?.();
        return;
      }

      if (input.ctrl && key === 'return') {
        onSubmit?.(internalValue);
        return;
      }

      if (key === 'return') {
        const newValue = internalValue + '\n';
        handleValueChange(newValue);
        return;
      }

      if (key === 'backspace') {
        if (internalValue.length > 0) {
          const newValue = internalValue.slice(0, -1);
          handleValueChange(newValue);
        }
        return;
      }

      // 普通字符输入
      if (key.length === 1) {
        const newValue = internalValue + key;
        handleValueChange(newValue);
      }
    };

    // 这里应该绑定到实际的键盘事件处理器
    
    return () => {
      // 清理事件监听器
    };
  }, [disabled, internalValue, handleValueChange, onSubmit, onCancel]);

  return React.createElement(Box, {
    flexDirection: 'column',
    borderStyle: 'round',
    borderColor: COLORS.secondaryBorder,
    borderDimColor: disabled,
    padding: 1,
    ...props
  },
    lines.map((line, index) => 
      React.createElement(Text, {
        key: index,
        color: line || (index === 0 && placeholder) ? undefined : COLORS.secondaryText
      }, line || (index === 0 ? placeholder : ''))
    )
  );
}

/**
 * 确认输入组件
 * @original: vM2组件相关逻辑
 * @param {Object} props - 组件属性
 * @returns {JSX.Element} 确认输入组件
 */
export function ConfirmInput({
  message = 'Are you sure?',
  placeholder = 'Type "yes" to confirm',
  confirmValue = 'yes',
  onConfirm,
  onCancel,
  caseSensitive = false,
  ...props
}) {
  const [inputValue, setInputValue] = React.useState('');

  const handleSubmit = React.useCallback((value) => {
    const compareValue = caseSensitive ? value : value.toLowerCase();
    const expectedValue = caseSensitive ? confirmValue : confirmValue.toLowerCase();

    if (compareValue === expectedValue) {
      onConfirm?.();
    } else {
      onCancel?.();
    }
  }, [confirmValue, caseSensitive, onConfirm, onCancel]);

  return React.createElement(Box, {
    flexDirection: 'column',
    gap: 1,
    ...props
  },
    React.createElement(Text, null, message),
    React.createElement(TextInput, {
      value: inputValue,
      placeholder,
      onChange: setInputValue,
      onSubmit: handleSubmit,
      onCancel
    })
  );
}
