/**
 * 认证管理服务
 * 
 * 管理用户认证、OAuth流程、API密钥和会话管理
 * 
 * @original: 认证相关代码 (L5193-5506, L55804-55926)
 */

import { logger } from '../utils/logger.js';
import { readFileSync, writeFileSync, existsSync } from 'fs';
import { join } from 'path';
import { homedir } from 'os';
import { logTelemetryEvent } from './telemetry.js';

/**
 * 认证类型枚举
 */
export const AUTH_TYPES = {
  API_KEY: 'api_key',
  OAUTH: 'oauth',
  CLAUDE_AI: 'claude_ai',
  CONSOLE: 'console',
  BEDROCK: 'bedrock',
  VERTEX: 'vertex'
};

/**
 * 认证状态枚举
 */
export const AUTH_STATUS = {
  AUTHENTICATED: 'authenticated',
  UNAUTHENTICATED: 'unauthenticated',
  EXPIRED: 'expired',
  INVALID: 'invalid',
  PENDING: 'pending'
};

/**
 * 订阅类型枚举
 * @original: tr1()函数 (L5330-5340)
 */
export const SUBSCRIPTION_TYPES = {
  MAX: 'max',
  PRO: 'pro',
  ENTERPRISE: 'enterprise',
  TEAM: 'team',
  FREE: 'free'
};

/**
 * OAuth配置
 * @original: OAuth相关常量 (L2398-2408)
 */
export const OAUTH_CONFIG = {
  BASE_API_URL: process.env.CLAUDE_CODE_BASE_API_URL || 'https://api.anthropic.com',
  CONSOLE_AUTHORIZE_URL: process.env.CLAUDE_CODE_CONSOLE_AUTHORIZE_URL || 'https://console.anthropic.com/oauth/authorize',
  CLAUDE_AI_AUTHORIZE_URL: process.env.CLAUDE_CODE_CLAUDE_AI_AUTHORIZE_URL || 'https://claude.ai/oauth/authorize',
  TOKEN_URL: process.env.CLAUDE_CODE_TOKEN_URL || 'https://api.anthropic.com/v1/oauth/token',
  API_KEY_URL: process.env.CLAUDE_CODE_API_KEY_URL || 'https://api.anthropic.com/api/oauth/claude_cli/create_api_key',
  ROLES_URL: process.env.CLAUDE_CODE_ROLES_URL || 'https://api.anthropic.com/api/oauth/claude_cli/roles',
  CONSOLE_SUCCESS_URL: process.env.CLAUDE_CODE_CONSOLE_SUCCESS_URL || 'https://console.anthropic.com/buy_credits?returnUrl=/oauth/code/success%3Fapp%3Dclaude-code',
  CLAUDEAI_SUCCESS_URL: process.env.CLAUDE_CODE_CLAUDEAI_SUCCESS_URL || 'https://claude.ai/oauth/code/success?app=claude-code',
  MANUAL_REDIRECT_URL: process.env.CLAUDE_CODE_MANUAL_REDIRECT_URL || 'https://console.anthropic.com/oauth/code/callback',
  CLIENT_ID: process.env.CLAUDE_CODE_CLIENT_ID || '22422756-60c9-4084-8eb7-27705fd5cf9a',
  REDIRECT_PORT: parseInt(process.env.CLAUDE_CODE_REDIRECT_PORT || '3000')
};

/**
 * 认证管理器类
 */
export class AuthManager {
  constructor() {
    this.currentAuth = null;
    this.authCache = new Map();
    this.tokenRefreshInterval = null;
  }

  /**
   * 检查认证状态
   * @original: 认证检查逻辑 (L5360-5374)
   * @returns {Promise<Object>} 认证状态信息
   */
  async checkAuthStatus() {
    try {
      // 检查API密钥
      if (process.env.ANTHROPIC_API_KEY) {
        return {
          status: AUTH_STATUS.AUTHENTICATED,
          type: AUTH_TYPES.API_KEY,
          source: 'ANTHROPIC_API_KEY',
          hasToken: true
        };
      }

      // 检查OAuth令牌
      if (process.env.CLAUDE_CODE_OAUTH_TOKEN) {
        return {
          status: AUTH_STATUS.AUTHENTICATED,
          type: AUTH_TYPES.OAUTH,
          source: 'CLAUDE_CODE_OAUTH_TOKEN',
          hasToken: true
        };
      }

      // 检查Claude.ai OAuth
      const claudeAiAuth = this.getClaudeAiAuth();
      if (this.isValidAuth(claudeAiAuth?.scopes) && claudeAiAuth?.accessToken) {
        return {
          status: AUTH_STATUS.AUTHENTICATED,
          type: AUTH_TYPES.CLAUDE_AI,
          source: 'claude.ai',
          hasToken: true,
          auth: claudeAiAuth
        };
      }

      return {
        status: AUTH_STATUS.UNAUTHENTICATED,
        type: null,
        source: null,
        hasToken: false
      };

    } catch (error) {
      logger.error('检查认证状态失败', { error: error.message });
      
      return {
        status: AUTH_STATUS.INVALID,
        type: null,
        source: null,
        hasToken: false,
        error: error.message
      };
    }
  }

  /**
   * 获取用户配置文件
   * @original: 用户配置获取逻辑 (L5193-5206)
   * @param {string} accessToken - 访问令牌
   * @param {string} refreshToken - 刷新令牌
   * @returns {Promise<Object|null>} 用户配置文件
   */
  async getUserProfile(accessToken, refreshToken) {
    if (!accessToken || !refreshToken) {
      return null;
    }

    const profileUrl = `${OAUTH_CONFIG.BASE_API_URL}/api/claude_cli_profile`;
    
    try {
      const axios = await import('axios');
      const response = await axios.default.get(profileUrl, {
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'User-Agent': this.getUserAgent()
        },
        timeout: 10000
      });

      if (response.status === 200 && response.data) {
        logger.debug('用户配置文件获取成功');
        return response.data;
      }

      return null;

    } catch (error) {
      logger.error('获取用户配置文件失败', { error: error.message });
      return null;
    }
  }

  /**
   * 获取订阅类型
   * @original: tr1()函数 (L5330-5340)
   * @param {string} accessToken - 访问令牌
   * @returns {Promise<string>} 订阅类型
   */
  async getSubscriptionType(accessToken) {
    try {
      const profile = await this.getUserProfile(accessToken);
      
      switch (profile?.organization?.organization_type) {
        case 'claude_max':
          return SUBSCRIPTION_TYPES.MAX;
        case 'claude_pro':
          return SUBSCRIPTION_TYPES.PRO;
        case 'claude_enterprise':
          return SUBSCRIPTION_TYPES.ENTERPRISE;
        case 'claude_team':
          return SUBSCRIPTION_TYPES.TEAM;
        default:
          return SUBSCRIPTION_TYPES.FREE;
      }

    } catch (error) {
      logger.error('获取订阅类型失败', { error: error.message });
      return SUBSCRIPTION_TYPES.FREE;
    }
  }

  /**
   * 获取订阅显示名称
   * @original: 订阅显示名称逻辑 (L5495-5507)
   * @param {string} subscriptionType - 订阅类型
   * @returns {string} 显示名称
   */
  getSubscriptionDisplayName(subscriptionType) {
    switch (subscriptionType) {
      case SUBSCRIPTION_TYPES.ENTERPRISE:
        return 'Claude Enterprise';
      case SUBSCRIPTION_TYPES.TEAM:
        return 'Claude Team';
      case SUBSCRIPTION_TYPES.MAX:
        return 'Claude Max';
      case SUBSCRIPTION_TYPES.PRO:
        return 'Claude Pro';
      default:
        return 'Claude API';
    }
  }

  /**
   * 保存Claude.ai OAuth认证
   * @original: 保存OAuth认证逻辑 (L5439-5448)
   * @param {Object} authData - 认证数据
   */
  saveClaudeAiAuth(authData) {
    try {
      const configManager = this.getConfigManager();
      const config = configManager.read() || {};
      
      config.claudeAiOauth = {
        accessToken: authData.accessToken,
        refreshToken: authData.refreshToken,
        expiresAt: authData.expiresAt,
        scopes: authData.scopes
      };

      configManager.write(config);
      
      logger.info('Claude.ai OAuth认证已保存');

    } catch (error) {
      logger.error('保存Claude.ai OAuth认证失败', { error: error.message });
      throw error;
    }
  }

  /**
   * 获取Claude.ai OAuth认证
   * @returns {Object|null} OAuth认证数据
   */
  getClaudeAiAuth() {
    try {
      const configManager = this.getConfigManager();
      const config = configManager.read() || {};
      
      return config.claudeAiOauth || null;

    } catch (error) {
      logger.error('获取Claude.ai OAuth认证失败', { error: error.message });
      return null;
    }
  }

  /**
   * 验证认证范围
   * @original: hy()函数相关逻辑
   * @param {Array} scopes - 认证范围
   * @returns {boolean} 认证范围是否有效
   */
  isValidAuth(scopes) {
    if (!scopes || !Array.isArray(scopes)) {
      return false;
    }

    // 检查必需的范围
    const requiredScopes = ['read', 'write'];
    return requiredScopes.every(scope => scopes.includes(scope));
  }

  /**
   * 刷新访问令牌
   * @param {string} refreshToken - 刷新令牌
   * @returns {Promise<Object>} 新的认证数据
   */
  async refreshAccessToken(refreshToken) {
    try {
      const axios = await import('axios');
      
      const response = await axios.default.post(OAUTH_CONFIG.TOKEN_URL, {
        grant_type: 'refresh_token',
        refresh_token: refreshToken,
        client_id: OAUTH_CONFIG.CLIENT_ID
      }, {
        headers: {
          'Content-Type': 'application/json',
          'User-Agent': this.getUserAgent()
        },
        timeout: 10000
      });

      if (response.status === 200 && response.data) {
        const authData = {
          accessToken: response.data.access_token,
          refreshToken: response.data.refresh_token || refreshToken,
          expiresAt: Date.now() + (response.data.expires_in * 1000),
          scopes: response.data.scope ? response.data.scope.split(' ') : []
        };

        this.saveClaudeAiAuth(authData);
        
        logger.info('访问令牌刷新成功');
        return authData;
      }

      throw new Error('令牌刷新响应无效');

    } catch (error) {
      logger.error('刷新访问令牌失败', { error: error.message });
      throw error;
    }
  }

  /**
   * 启动OAuth流程
   * @original: OAuth流程逻辑 (L5230-5235)
   * @param {Object} options - OAuth选项
   * @returns {Promise<Object>} OAuth结果
   */
  async startOAuthFlow(options = {}) {
    const {
      state,
      isManual = false,
      loginWithClaudeAi = false,
      inferenceOnly = false
    } = options;

    try {
      const authorizeUrl = loginWithClaudeAi ? 
        OAUTH_CONFIG.CLAUDE_AI_AUTHORIZE_URL : 
        OAUTH_CONFIG.CONSOLE_AUTHORIZE_URL;

      const url = new URL(authorizeUrl);
      url.searchParams.append('code', 'true');
      url.searchParams.append('client_id', OAUTH_CONFIG.CLIENT_ID);
      url.searchParams.append('response_type', 'code');
      url.searchParams.append('redirect_uri', 
        isManual ? 
          OAUTH_CONFIG.MANUAL_REDIRECT_URL : 
          `http://localhost:${OAUTH_CONFIG.REDIRECT_PORT}/callback`
      );

      if (state) {
        url.searchParams.append('state', state);
      }

      if (inferenceOnly) {
        url.searchParams.append('scope', 'inference');
      }

      logger.info('启动OAuth流程', { 
        loginWithClaudeAi, 
        isManual, 
        inferenceOnly 
      });

      return {
        authorizeUrl: url.toString(),
        state,
        isManual,
        loginWithClaudeAi
      };

    } catch (error) {
      logger.error('启动OAuth流程失败', { error: error.message });
      throw error;
    }
  }

  /**
   * 检查是否使用云服务
   * @original: ZsA()函数 (L5487-5489)
   * @returns {boolean} 是否使用云服务
   */
  isUsingCloudService() {
    if (process.env.CLAUDE_CODE_USE_BEDROCK === 'true' || 
        process.env.CLAUDE_CODE_USE_VERTEX === 'true') {
      return false;
    }
    
    // 检查是否为企业用户
    if (this.isEnterpriseUser()) {
      return false;
    }
    
    return true;
  }

  /**
   * 检查是否为企业用户
   * @returns {boolean} 是否为企业用户
   */
  isEnterpriseUser() {
    // 这里需要实现企业用户检查逻辑
    return false;
  }

  /**
   * 获取认证提供商名称
   * @original: 认证提供商逻辑 (L5495-5507)
   * @returns {string} 认证提供商名称
   */
  getAuthProviderName() {
    const subscriptionType = this.getCurrentSubscriptionType();
    
    switch (subscriptionType) {
      case SUBSCRIPTION_TYPES.ENTERPRISE:
        return 'Claude Enterprise';
      case SUBSCRIPTION_TYPES.TEAM:
        return 'Claude Team';
      case SUBSCRIPTION_TYPES.MAX:
        return 'Claude Max';
      case SUBSCRIPTION_TYPES.PRO:
        return 'Claude Pro';
      default:
        return 'Claude API';
    }
  }

  /**
   * 检查是否使用AWS Bedrock或Google Vertex
   * @original: ga()函数 (L5509)
   * @returns {boolean} 是否使用第三方云服务
   */
  isUsingThirdPartyCloud() {
    return !!(process.env.CLAUDE_CODE_USE_BEDROCK || process.env.CLAUDE_CODE_USE_VERTEX);
  }

  /**
   * 获取mTLS配置
   * @original: BP变量 (L5538-5556)
   * @returns {Object|null} mTLS配置
   */
  getMTLSConfig() {
    const config = {};

    if (process.env.CLAUDE_CODE_CLIENT_CERT) {
      try {
        config.cert = readFileSync(process.env.CLAUDE_CODE_CLIENT_CERT, { encoding: 'utf8' });
        logger.debug('mTLS: 已从CLAUDE_CODE_CLIENT_CERT加载客户端证书');
      } catch (error) {
        logger.error(`mTLS: 加载客户端证书失败: ${error.message}`);
      }
    }

    if (process.env.CLAUDE_CODE_CLIENT_KEY) {
      try {
        config.key = readFileSync(process.env.CLAUDE_CODE_CLIENT_KEY, { encoding: 'utf8' });
        logger.debug('mTLS: 已从CLAUDE_CODE_CLIENT_KEY加载客户端密钥');
      } catch (error) {
        logger.error(`mTLS: 加载客户端密钥失败: ${error.message}`);
      }
    }

    if (process.env.CLAUDE_CODE_CLIENT_KEY_PASSPHRASE) {
      config.passphrase = process.env.CLAUDE_CODE_CLIENT_KEY_PASSPHRASE;
      logger.debug('mTLS: 使用客户端密钥密码');
    }

    if (Object.keys(config).length === 0) {
      return null;
    }

    return config;
  }

  /**
   * 获取配置管理器
   * @returns {Object} 配置管理器实例
   */
  getConfigManager() {
    // 这里需要导入配置管理器
    // 为了避免循环依赖，使用动态导入
    const { globalConfigManager } = require('../config/settings.js');
    return globalConfigManager;
  }

  /**
   * 获取用户代理字符串
   * @returns {string} 用户代理字符串
   */
  getUserAgent() {
    const { APP_INFO } = require('../config/constants.js');
    return `claude-code/${APP_INFO.VERSION} (${process.platform}; ${process.arch})`;
  }

  /**
   * 获取当前订阅类型
   * @returns {string} 当前订阅类型
   */
  getCurrentSubscriptionType() {
    // 这里需要实现获取当前订阅类型的逻辑
    return SUBSCRIPTION_TYPES.FREE;
  }

  /**
   * 验证API密钥
   * @param {string} apiKey - API密钥
   * @returns {Promise<boolean>} API密钥是否有效
   */
  async validateAPIKey(apiKey) {
    try {
      const axios = await import('axios');
      
      const response = await axios.default.get(`${OAUTH_CONFIG.BASE_API_URL}/v1/models`, {
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          'User-Agent': this.getUserAgent()
        },
        timeout: 10000
      });

      return response.status === 200;

    } catch (error) {
      logger.error('API密钥验证失败', { error: error.message });
      return false;
    }
  }

  /**
   * 登出
   * @returns {Promise<void>}
   */
  async logout() {
    try {
      // 清除OAuth认证
      const configManager = this.getConfigManager();
      const config = configManager.read() || {};
      
      if (config.claudeAiOauth) {
        delete config.claudeAiOauth;
        configManager.write(config);
      }

      // 清除环境变量（如果是通过代码设置的）
      delete process.env.ANTHROPIC_API_KEY;
      delete process.env.CLAUDE_CODE_OAUTH_TOKEN;

      // 清除缓存
      this.authCache.clear();
      this.currentAuth = null;

      // 停止令牌刷新
      if (this.tokenRefreshInterval) {
        clearInterval(this.tokenRefreshInterval);
        this.tokenRefreshInterval = null;
      }

      logger.info('用户已登出');
      
      logTelemetryEvent('user_logout', {});

    } catch (error) {
      logger.error('登出失败', { error: error.message });
      throw error;
    }
  }

  /**
   * 启动自动令牌刷新
   * @param {number} intervalMs - 刷新间隔（毫秒）
   */
  startTokenRefresh(intervalMs = 30 * 60 * 1000) { // 30分钟
    if (this.tokenRefreshInterval) {
      clearInterval(this.tokenRefreshInterval);
    }

    this.tokenRefreshInterval = setInterval(async () => {
      try {
        const claudeAiAuth = this.getClaudeAiAuth();
        
        if (claudeAiAuth?.refreshToken) {
          // 检查是否需要刷新（提前5分钟刷新）
          const expiresAt = claudeAiAuth.expiresAt || 0;
          const refreshThreshold = Date.now() + (5 * 60 * 1000);
          
          if (expiresAt < refreshThreshold) {
            await this.refreshAccessToken(claudeAiAuth.refreshToken);
            logger.debug('访问令牌已自动刷新');
          }
        }

      } catch (error) {
        logger.error('自动令牌刷新失败', { error: error.message });
      }
    }, intervalMs);

    logger.debug('自动令牌刷新已启动', { intervalMs });
  }

  /**
   * 停止自动令牌刷新
   */
  stopTokenRefresh() {
    if (this.tokenRefreshInterval) {
      clearInterval(this.tokenRefreshInterval);
      this.tokenRefreshInterval = null;
      logger.debug('自动令牌刷新已停止');
    }
  }
}

/**
 * OAuth流程管理器类
 */
export class OAuthFlowManager {
  constructor(authManager) {
    this.authManager = authManager || new AuthManager();
    this.activeFlows = new Map();
  }

  /**
   * 创建OAuth授权URL
   * @param {Object} options - OAuth选项
   * @returns {string} 授权URL
   */
  createAuthorizationURL(options = {}) {
    const flow = this.authManager.startOAuthFlow(options);
    return flow.authorizeUrl;
  }

  /**
   * 处理OAuth回调
   * @param {string} code - 授权码
   * @param {string} state - 状态参数
   * @returns {Promise<Object>} 认证结果
   */
  async handleOAuthCallback(code, state) {
    try {
      const axios = await import('axios');
      
      const response = await axios.default.post(OAUTH_CONFIG.TOKEN_URL, {
        grant_type: 'authorization_code',
        code,
        client_id: OAUTH_CONFIG.CLIENT_ID,
        redirect_uri: `http://localhost:${OAUTH_CONFIG.REDIRECT_PORT}/callback`
      }, {
        headers: {
          'Content-Type': 'application/json',
          'User-Agent': this.authManager.getUserAgent()
        },
        timeout: 10000
      });

      if (response.status === 200 && response.data) {
        const authData = {
          accessToken: response.data.access_token,
          refreshToken: response.data.refresh_token,
          expiresAt: Date.now() + (response.data.expires_in * 1000),
          scopes: response.data.scope ? response.data.scope.split(' ') : []
        };

        this.authManager.saveClaudeAiAuth(authData);
        
        logger.info('OAuth认证成功');
        
        logTelemetryEvent('oauth_success', {
          provider: 'claude_ai'
        });

        return {
          success: true,
          authData
        };
      }

      throw new Error('OAuth令牌交换失败');

    } catch (error) {
      logger.error('OAuth回调处理失败', { error: error.message });
      
      logTelemetryEvent('oauth_error', {
        provider: 'claude_ai',
        error: error.message
      });

      throw error;
    }
  }
}

/**
 * 全局认证管理器实例
 */
export const globalAuthManager = new AuthManager();

/**
 * 全局OAuth流程管理器实例
 */
export const globalOAuthFlowManager = new OAuthFlowManager(globalAuthManager);

/**
 * 便捷函数：检查认证状态
 * @returns {Promise<Object>} 认证状态信息
 */
export async function checkAuthStatus() {
  return globalAuthManager.checkAuthStatus();
}

/**
 * 便捷函数：获取用户配置文件
 * @param {string} accessToken - 访问令牌
 * @param {string} refreshToken - 刷新令牌
 * @returns {Promise<Object|null>} 用户配置文件
 */
export async function getUserProfile(accessToken, refreshToken) {
  return globalAuthManager.getUserProfile(accessToken, refreshToken);
}

/**
 * 便捷函数：获取订阅类型
 * @param {string} accessToken - 访问令牌
 * @returns {Promise<string>} 订阅类型
 */
export async function getSubscriptionType(accessToken) {
  return globalAuthManager.getSubscriptionType(accessToken);
}

/**
 * 便捷函数：启动OAuth流程
 * @param {Object} options - OAuth选项
 * @returns {Promise<Object>} OAuth结果
 */
export async function startOAuthFlow(options = {}) {
  return globalAuthManager.startOAuthFlow(options);
}

/**
 * 便捷函数：处理OAuth回调
 * @param {string} code - 授权码
 * @param {string} state - 状态参数
 * @returns {Promise<Object>} 认证结果
 */
export async function handleOAuthCallback(code, state) {
  return globalOAuthFlowManager.handleOAuthCallback(code, state);
}

/**
 * 便捷函数：登出
 * @returns {Promise<void>}
 */
export async function logout() {
  return globalAuthManager.logout();
}
