/**
 * 代码搜索服务
 * 
 * 基于ripgrep的高性能代码搜索功能
 * 
 * @original: 行2700-2785的ripgrep集成代码
 */

import { execFile } from 'child_process';
import { join, resolve } from 'path';
import { fileURLToPath } from 'node:url';
import { logger } from '../utils/logger.js';
import { executeCommand } from '../utils/process.js';

/**
 * 检查是否在Bun环境中运行
 * @original: Kw()函数 (L2701)
 * @returns {boolean} 是否在Bun环境中
 */
function isRunningInBun() {
  return typeof Bun !== "undefined" && 
         !!Bun?.embeddedFiles && 
         Array.isArray(Bun?.embeddedFiles) && 
         (Bun?.embeddedFiles?.length ?? 0) > 0;
}

/**
 * 获取ripgrep可执行文件路径和参数
 * @original: Ts1()函数 (L2716)
 * @returns {Object} 包含rgPath和rgArgs的对象
 */
function getRipgrepConfig() {
  if (isRunningInBun()) {
    return {
      rgPath: process.execPath,
      rgArgs: ["--ripgrep"]
    };
  }

  // 获取内置ripgrep路径
  const currentFileUrl = import.meta.url;
  const currentFilePath = fileURLToPath(currentFileUrl);
  const baseDir = join(currentFilePath, "../../../");
  
  const ripgrepDir = resolve(baseDir, "vendor", "ripgrep");
  
  if (process.platform === "win32") {
    return {
      rgPath: resolve(ripgrepDir, "x64-win32", "rg.exe"),
      rgArgs: []
    };
  }
  
  return {
    rgPath: resolve(ripgrepDir, `${process.arch}-${process.platform}`, "rg"),
    rgArgs: []
  };
}

/**
 * 执行ripgrep命令
 * @original: f0Q()函数 (L2722)
 * @param {string[]} args - ripgrep参数
 * @param {string} searchPath - 搜索路径
 * @param {AbortSignal} signal - 中止信号
 * @param {Function} callback - 回调函数
 * @returns {ChildProcess} 子进程对象
 */
function executeRipgrepCommand(args, searchPath, signal, callback) {
  const { rgPath, rgArgs } = getRipgrepConfig();
  
  return execFile(rgPath, [...rgArgs, ...args, searchPath], {
    maxBuffer: 20000000, // 20MB缓冲区
    signal: signal,
    timeout: 10000 // 10秒超时
  }, callback);
}

/**
 * 执行ripgrep搜索
 * @original: PO()函数 (L2733)
 * @param {string[]} args - ripgrep参数
 * @param {string} searchPath - 搜索路径
 * @param {AbortSignal} signal - 中止信号
 * @returns {Promise<string[]>} 搜索结果行数组
 */
export async function executeRipgrep(args, searchPath, signal) {
  // 在非Bun环境中，确保ripgrep已签名（macOS）
  if (!isRunningInBun()) {
    await ensureRipgrepSigned();
  }

  return new Promise(resolve => {
    executeRipgrepCommand(args, searchPath, signal, (error, stdout, stderr) => {
      if (error) {
        // 处理不同的退出码
        if (error.code !== 1 && error.code !== 2) {
          logger.error('ripgrep执行失败:', error);
          resolve([]);
        } else if (error.code === 2 && stdout && stdout.trim().length > 0) {
          // 退出码2但有输出，返回结果
          resolve(stdout.trim().split('\n').filter(Boolean));
        } else {
          if (error.code === 2) {
            logger.error('ripgrep错误(2)，无输出:', JSON.stringify(error), stderr);
          }
          resolve([]);
        }
      } else {
        resolve(stdout.trim().split('\n').filter(Boolean));
      }
    });
  });
}

/**
 * 搜索文件列表
 * @original: k6A()函数 (L2748)
 * @param {string} searchPath - 搜索路径
 * @param {AbortSignal} signal - 中止信号
 * @param {number} limit - 结果数量限制
 * @returns {Promise<string[]>} 文件路径数组
 */
export async function searchFiles(searchPath, signal, limit) {
  try {
    const results = await executeRipgrep(["-l", "."], searchPath, signal);
    return results.slice(0, limit);
  } catch (error) {
    logger.error('文件搜索失败:', error);
    return [];
  }
}

/**
 * 统计目录中的文件数量（估算）
 * @original: uC1变量 (L2755)
 * @param {string} searchPath - 搜索路径
 * @param {AbortSignal} signal - 中止信号
 * @param {string[]} excludePatterns - 排除模式
 * @returns {Promise<number>} 估算的文件数量
 */
export async function estimateFileCount(searchPath, signal, excludePatterns = []) {
  try {
    const args = ["--files", "--hidden"];
    
    // 添加排除模式
    excludePatterns.forEach(pattern => {
      args.push("--glob", `!${pattern}`);
    });

    const results = await executeRipgrep(args, searchPath, signal);
    const totalCount = results.length;

    if (totalCount === 0) return 0;

    // 返回四舍五入到最近的10的幂
    const magnitude = Math.floor(Math.log10(totalCount));
    const base = Math.pow(10, magnitude);
    return Math.round(totalCount / base) * base;
    
  } catch (error) {
    logger.error('文件计数失败:', error);
    return 0;
  }
}

/**
 * 确保ripgrep在macOS上已签名
 * @original: h0Q()函数 (L2771)
 * @returns {Promise<void>}
 */
let ripgrepSigned = false;
async function ensureRipgrepSigned() {
  if (process.platform !== "darwin" || ripgrepSigned) {
    return;
  }

  ripgrepSigned = true;
  const { rgPath } = getRipgrepConfig();

  try {
    // 检查是否需要签名
    const codesignCheck = await executeCommand("codesign", ["-vv", "-d", rgPath], {
      preserveOutputOnError: false
    });

    const needsSigning = !codesignCheck.stdout
      .split('\n')
      .find(line => line.includes("linker-signed"));

    if (!needsSigning) return;

    // 对ripgrep进行签名
    logger.debug('对ripgrep进行代码签名');
    const signResult = await executeCommand("codesign", [
      "--sign", "-", "--force", 
      "--preserve-metadata=entitlements,requirements,flags,runtime", 
      rgPath
    ]);

    if (signResult.code !== 0) {
      throw new Error(`签名失败: ${signResult.stdout} ${signResult.stderr}`);
    }

    // 移除隔离属性
    const xattrResult = await executeCommand("xattr", ["-d", "com.apple.quarantine", rgPath]);
    
    if (xattrResult.code !== 0) {
      throw new Error(`移除隔离属性失败: ${xattrResult.stdout} ${xattrResult.stderr}`);
    }

    logger.debug('ripgrep签名完成');
    
  } catch (error) {
    logger.error('ripgrep签名失败:', error);
  }
}

/**
 * 在文本中搜索模式
 * @param {string} pattern - 搜索模式
 * @param {string} searchPath - 搜索路径
 * @param {Object} options - 搜索选项
 * @returns {Promise<string[]>} 匹配的行
 */
export async function searchInFiles(pattern, searchPath, options = {}) {
  const {
    caseSensitive = false,
    wholeWord = false,
    regex = false,
    maxResults = 1000,
    signal = null
  } = options;

  const args = [];
  
  if (!caseSensitive) args.push("-i");
  if (wholeWord) args.push("-w");
  if (!regex) args.push("-F"); // 固定字符串搜索
  if (maxResults) args.push("-m", maxResults.toString());
  
  args.push(pattern);

  try {
    return await executeRipgrep(args, searchPath, signal);
  } catch (error) {
    logger.error('文本搜索失败:', error);
    return [];
  }
}
