/**
 * PDF处理服务
 * 
 * 提供PDF文件读取、解析和处理功能
 * 
 * @original: PDF处理相关代码 (L2926-2947, L25262-25570)
 */

import { readFileSync, statSync } from 'fs';
import { logger } from '../utils/logger.js';
import { FILE_READING } from '../config/constants.js';

/**
 * 支持的PDF文件扩展名
 * @original: o6Q变量 (L2926)
 */
export const PDF_EXTENSIONS = new Set(['pdf']);

/**
 * PDF文件最大大小（32MB）
 * @original: WIA变量 (L2927)
 */
export const MAX_PDF_SIZE = 33554432; // 32MB

/**
 * PDF文件信息接口
 */
export class PDFFileInfo {
  constructor(filePath, base64Data, originalSize) {
    this.type = 'pdf';
    this.file = {
      filePath,
      base64: base64Data,
      originalSize
    };
  }

  /**
   * 获取格式化的文件大小
   * @returns {string} 格式化的文件大小
   */
  getFormattedSize() {
    return formatFileSize(this.file.originalSize);
  }

  /**
   * 转换为Claude API格式
   * @returns {Object} Claude API格式的文档对象
   */
  toClaudeFormat() {
    return {
      type: 'document',
      source: {
        type: 'base64',
        media_type: 'application/pdf',
        data: this.file.base64
      }
    };
  }

  /**
   * 转换为工具结果格式
   * @param {string} toolUseId - 工具使用ID
   * @returns {Object} 工具结果对象
   */
  toToolResult(toolUseId) {
    return {
      tool_use_id: toolUseId,
      type: 'tool_result',
      content: `PDF file read: ${this.file.filePath} (${this.getFormattedSize()})`
    };
  }
}

/**
 * 检查文件是否为PDF
 * @original: 文件类型检查逻辑
 * @param {string} filePath - 文件路径
 * @returns {boolean} 是否为PDF文件
 */
export function isPDFFile(filePath) {
  const extension = filePath.toLowerCase().split('.').pop();
  return PDF_EXTENSIONS.has(extension);
}

/**
 * 验证PDF文件
 * @param {string} filePath - 文件路径
 * @throws {Error} 文件无效时抛出错误
 */
function validatePDFFile(filePath) {
  let stats;
  
  try {
    stats = statSync(filePath);
  } catch (error) {
    throw new Error(`PDF file not found: ${filePath}`);
  }

  if (!stats.isFile()) {
    throw new Error(`Path is not a file: ${filePath}`);
  }

  if (stats.size === 0) {
    throw new Error(`PDF file is empty: ${filePath}`);
  }

  if (stats.size > MAX_PDF_SIZE) {
    const currentSize = formatFileSize(stats.size);
    const maxSize = formatFileSize(MAX_PDF_SIZE);
    throw new Error(
      `PDF file size (${currentSize}) exceeds maximum allowed size (${maxSize}). ` +
      `PDF files must be less than 32MB.`
    );
  }
}

/**
 * 读取PDF文件
 * @original: JIA()函数 (L2935)
 * @param {string} filePath - PDF文件路径
 * @returns {Promise<PDFFileInfo>} PDF文件信息
 * @throws {Error} 读取失败时抛出错误
 */
export async function readPDFFile(filePath) {
  logger.debug('读取PDF文件', { filePath });

  try {
    // 验证文件
    validatePDFFile(filePath);

    // 读取文件并转换为base64
    const fileBuffer = readFileSync(filePath);
    const base64Data = fileBuffer.toString('base64');
    const fileSize = fileBuffer.length;

    logger.debug('PDF文件读取成功', { 
      filePath, 
      size: fileSize,
      base64Length: base64Data.length 
    });

    return new PDFFileInfo(filePath, base64Data, fileSize);

  } catch (error) {
    logger.error('PDF文件读取失败', { filePath, error: error.message });
    throw error;
  }
}

/**
 * 批量读取PDF文件
 * @param {string[]} filePaths - PDF文件路径数组
 * @returns {Promise<PDFFileInfo[]>} PDF文件信息数组
 */
export async function readMultiplePDFFiles(filePaths) {
  logger.debug('批量读取PDF文件', { count: filePaths.length });

  const results = [];
  const errors = [];

  for (const filePath of filePaths) {
    try {
      const pdfInfo = await readPDFFile(filePath);
      results.push(pdfInfo);
    } catch (error) {
      logger.warn('PDF文件读取失败', { filePath, error: error.message });
      errors.push({ filePath, error: error.message });
    }
  }

  if (errors.length > 0) {
    logger.warn('部分PDF文件读取失败', { 
      successCount: results.length,
      errorCount: errors.length,
      errors 
    });
  }

  return results;
}

/**
 * 获取PDF文件元数据
 * @param {string} filePath - PDF文件路径
 * @returns {Promise<Object>} PDF元数据
 */
export async function getPDFMetadata(filePath) {
  logger.debug('获取PDF元数据', { filePath });

  try {
    validatePDFFile(filePath);
    
    const stats = statSync(filePath);
    
    return {
      filePath,
      size: stats.size,
      formattedSize: formatFileSize(stats.size),
      lastModified: stats.mtime,
      created: stats.birthtime,
      isValidSize: stats.size <= MAX_PDF_SIZE,
      maxAllowedSize: MAX_PDF_SIZE,
      extension: 'pdf'
    };

  } catch (error) {
    logger.error('获取PDF元数据失败', { filePath, error: error.message });
    throw error;
  }
}

/**
 * 检查PDF文件是否可读
 * @param {string} filePath - PDF文件路径
 * @returns {Promise<Object>} 检查结果
 */
export async function checkPDFReadability(filePath) {
  const result = {
    readable: false,
    error: null,
    metadata: null
  };

  try {
    result.metadata = await getPDFMetadata(filePath);
    result.readable = true;
  } catch (error) {
    result.error = error.message;
  }

  return result;
}

/**
 * 格式化文件大小
 * @original: wY()函数
 * @param {number} bytes - 字节数
 * @returns {string} 格式化的文件大小
 */
function formatFileSize(bytes) {
  if (bytes === 0) return '0 B';
  
  const units = ['B', 'KB', 'MB', 'GB'];
  const k = 1024;
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return `${(bytes / Math.pow(k, i)).toFixed(1)} ${units[i]}`;
}

/**
 * PDF处理器类
 * 提供更高级的PDF处理功能
 */
export class PDFProcessor {
  constructor(options = {}) {
    this.maxFileSize = options.maxFileSize || MAX_PDF_SIZE;
    this.allowedExtensions = options.allowedExtensions || PDF_EXTENSIONS;
  }

  /**
   * 处理PDF文件
   * @param {string} filePath - PDF文件路径
   * @returns {Promise<PDFFileInfo>} 处理结果
   */
  async process(filePath) {
    logger.debug('处理PDF文件', { filePath });

    // 检查扩展名
    if (!this.isAllowedExtension(filePath)) {
      throw new Error(`Unsupported file extension. Allowed: ${Array.from(this.allowedExtensions).join(', ')}`);
    }

    // 读取PDF
    const pdfInfo = await readPDFFile(filePath);

    // 额外的处理逻辑可以在这里添加
    // 例如：提取文本、分析内容等

    return pdfInfo;
  }

  /**
   * 检查文件扩展名是否被允许
   * @param {string} filePath - 文件路径
   * @returns {boolean} 是否允许
   */
  isAllowedExtension(filePath) {
    const extension = filePath.toLowerCase().split('.').pop();
    return this.allowedExtensions.has(extension);
  }

  /**
   * 批量处理PDF文件
   * @param {string[]} filePaths - 文件路径数组
   * @returns {Promise<Object>} 处理结果
   */
  async processBatch(filePaths) {
    logger.debug('批量处理PDF文件', { count: filePaths.length });

    const results = [];
    const errors = [];

    for (const filePath of filePaths) {
      try {
        const result = await this.process(filePath);
        results.push(result);
      } catch (error) {
        errors.push({ filePath, error: error.message });
      }
    }

    return {
      success: results,
      errors,
      totalProcessed: results.length,
      totalErrors: errors.length
    };
  }
}

/**
 * 默认PDF处理器实例
 */
export const defaultPDFProcessor = new PDFProcessor();

/**
 * 便捷函数：处理PDF文件
 * @param {string} filePath - PDF文件路径
 * @returns {Promise<PDFFileInfo>} PDF文件信息
 */
export function processPDF(filePath) {
  return defaultPDFProcessor.process(filePath);
}

/**
 * 便捷函数：批量处理PDF文件
 * @param {string[]} filePaths - PDF文件路径数组
 * @returns {Promise<Object>} 处理结果
 */
export function processPDFBatch(filePaths) {
  return defaultPDFProcessor.processBatch(filePaths);
}
