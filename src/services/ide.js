/**
 * IDE集成服务
 * 
 * 管理Claude Code与各种IDE的集成，包括扩展安装、配置、通信等
 * 
 * @original: IDE集成相关代码 (L13267-13284, L13749-13772, L13862-13890)
 */

import { logger } from '../utils/logger.js';
import { executeCommand } from '../utils/process.js';
import { readFileSync, writeFileSync, existsSync } from 'fs';
import { join, dirname } from 'path';
import { homedir } from 'os';

/**
 * 支持的IDE类型
 * @original: LH0对象 (L13267)
 */
export const SUPPORTED_IDES = {
  VSCODE: 'vscode',
  CURSOR: 'cursor',
  WINDSURF: 'windsurf',
  INTELLIJ: 'intellij',
  PYCHARM: 'pycharm',
  WEBSTORM: 'webstorm',
  PHPSTORM: 'phpstorm',
  RUBYMINE: 'rubymine',
  CLION: 'clion',
  GOLAND: 'goland',
  RIDER: 'rider',
  ANDROID_STUDIO: 'android_studio'
};

/**
 * IDE安装状态枚举
 */
export const IDE_INSTALL_STATUS = {
  NOT_INSTALLED: 'not_installed',
  INSTALLING: 'installing',
  INSTALLED: 'installed',
  FAILED: 'failed',
  UNKNOWN: 'unknown'
};

/**
 * IDE检测器类
 */
export class IDEDetector {
  constructor() {
    this.detectionCache = new Map();
    this.cacheTimeout = 60000; // 1分钟缓存
  }

  /**
   * 检测已安装的IDE
   * @returns {Promise<Array>} 已安装的IDE列表
   */
  async detectInstalledIDEs() {
    const installedIDEs = [];

    for (const [ideType, ideName] of Object.entries(SUPPORTED_IDES)) {
      try {
        const isInstalled = await this.isIDEInstalled(ideType);
        
        if (isInstalled) {
          const ideInfo = await this.getIDEInfo(ideType);
          installedIDEs.push({
            type: ideType,
            name: ideName,
            ...ideInfo
          });
        }
      } catch (error) {
        logger.warn('IDE检测失败', { ideType, error: error.message });
      }
    }

    logger.debug('IDE检测完成', { count: installedIDEs.length });
    
    return installedIDEs;
  }

  /**
   * 检查IDE是否已安装
   * @param {string} ideType - IDE类型
   * @returns {Promise<boolean>} 是否已安装
   */
  async isIDEInstalled(ideType) {
    const cacheKey = `installed_${ideType}`;
    const cached = this.getFromCache(cacheKey);
    
    if (cached !== null) {
      return cached;
    }

    let isInstalled = false;

    try {
      switch (ideType) {
        case SUPPORTED_IDES.VSCODE:
          isInstalled = await this.checkVSCodeInstallation();
          break;
        case SUPPORTED_IDES.CURSOR:
          isInstalled = await this.checkCursorInstallation();
          break;
        case SUPPORTED_IDES.WINDSURF:
          isInstalled = await this.checkWindsurfInstallation();
          break;
        default:
          if (this.isJetBrainsIDE(ideType)) {
            isInstalled = await this.checkJetBrainsInstallation(ideType);
          }
      }
    } catch (error) {
      logger.warn('IDE安装检查失败', { ideType, error: error.message });
    }

    this.setCache(cacheKey, isInstalled);
    return isInstalled;
  }

  /**
   * 检查VSCode安装
   * @returns {Promise<boolean>} 是否已安装
   */
  async checkVSCodeInstallation() {
    try {
      const result = await executeCommand('code', ['--version'], { timeout: 5000 });
      return result.code === 0;
    } catch (error) {
      return false;
    }
  }

  /**
   * 检查Cursor安装
   * @original: uDB变量 (L13975)
   * @returns {Promise<boolean>} 是否已安装
   */
  async checkCursorInstallation() {
    try {
      const result = await executeCommand('cursor', ['--version'], { timeout: 5000 });
      return result.code === 0;
    } catch (error) {
      return false;
    }
  }

  /**
   * 检查Windsurf安装
   * @original: mDB变量 (L13982)
   * @returns {Promise<boolean>} 是否已安装
   */
  async checkWindsurfInstallation() {
    try {
      const result = await executeCommand('windsurf', ['--version'], { timeout: 5000 });
      return result.code === 0;
    } catch (error) {
      return false;
    }
  }

  /**
   * 检查JetBrains IDE安装
   * @param {string} ideType - IDE类型
   * @returns {Promise<boolean>} 是否已安装
   */
  async checkJetBrainsInstallation(ideType) {
    // 实现JetBrains IDE检测逻辑
    return false;
  }

  /**
   * 检查是否为JetBrains IDE
   * @param {string} ideType - IDE类型
   * @returns {boolean} 是否为JetBrains IDE
   */
  isJetBrainsIDE(ideType) {
    const jetbrainsIDEs = [
      SUPPORTED_IDES.INTELLIJ,
      SUPPORTED_IDES.PYCHARM,
      SUPPORTED_IDES.WEBSTORM,
      SUPPORTED_IDES.PHPSTORM,
      SUPPORTED_IDES.RUBYMINE,
      SUPPORTED_IDES.CLION,
      SUPPORTED_IDES.GOLAND,
      SUPPORTED_IDES.RIDER,
      SUPPORTED_IDES.ANDROID_STUDIO
    ];
    
    return jetbrainsIDEs.includes(ideType);
  }

  /**
   * 获取IDE信息
   * @param {string} ideType - IDE类型
   * @returns {Promise<Object>} IDE信息
   */
  async getIDEInfo(ideType) {
    try {
      const version = await this.getIDEVersion(ideType);
      const extensionStatus = await this.getExtensionStatus(ideType);
      
      return {
        version,
        extensionStatus,
        lastChecked: new Date().toISOString()
      };
    } catch (error) {
      return {
        version: null,
        extensionStatus: IDE_INSTALL_STATUS.UNKNOWN,
        error: error.message
      };
    }
  }

  /**
   * 获取IDE版本
   * @param {string} ideType - IDE类型
   * @returns {Promise<string|null>} IDE版本
   */
  async getIDEVersion(ideType) {
    try {
      const command = this.getVersionCommand(ideType);
      if (!command) return null;

      const result = await executeCommand(command.cmd, command.args, { timeout: 5000 });
      
      if (result.code === 0 && result.stdout) {
        return this.parseVersionFromOutput(result.stdout, ideType);
      }

      return null;
    } catch (error) {
      return null;
    }
  }

  /**
   * 获取版本检查命令
   * @param {string} ideType - IDE类型
   * @returns {Object|null} 命令对象
   */
  getVersionCommand(ideType) {
    const commands = {
      [SUPPORTED_IDES.VSCODE]: { cmd: 'code', args: ['--version'] },
      [SUPPORTED_IDES.CURSOR]: { cmd: 'cursor', args: ['--version'] },
      [SUPPORTED_IDES.WINDSURF]: { cmd: 'windsurf', args: ['--version'] }
    };

    return commands[ideType] || null;
  }

  /**
   * 从输出中解析版本
   * @param {string} output - 命令输出
   * @param {string} ideType - IDE类型
   * @returns {string|null} 解析的版本
   */
  parseVersionFromOutput(output, ideType) {
    const lines = output.trim().split('\n');
    
    if (lines.length > 0) {
      return lines[0].trim();
    }

    return null;
  }

  /**
   * 获取扩展状态
   * @param {string} ideType - IDE类型
   * @returns {Promise<string>} 扩展状态
   */
  async getExtensionStatus(ideType) {
    try {
      const isInstalled = await this.isExtensionInstalled(ideType);
      return isInstalled ? IDE_INSTALL_STATUS.INSTALLED : IDE_INSTALL_STATUS.NOT_INSTALLED;
    } catch (error) {
      return IDE_INSTALL_STATUS.UNKNOWN;
    }
  }

  /**
   * 检查扩展是否已安装
   * @param {string} ideType - IDE类型
   * @returns {Promise<boolean>} 是否已安装
   */
  async isExtensionInstalled(ideType) {
    switch (ideType) {
      case SUPPORTED_IDES.VSCODE:
      case SUPPORTED_IDES.CURSOR:
      case SUPPORTED_IDES.WINDSURF:
        return await this.checkVSCodeExtension(ideType);
      default:
        if (this.isJetBrainsIDE(ideType)) {
          return await this.checkJetBrainsExtension(ideType);
        }
        return false;
    }
  }

  /**
   * 检查VSCode类扩展
   * @original: cx6()函数 (L13911)
   * @param {string} ideType - IDE类型
   * @returns {Promise<boolean>} 是否已安装
   */
  async checkVSCodeExtension(ideType) {
    try {
      const command = this.getExtensionListCommand(ideType);
      if (!command) return false;

      const result = await executeCommand(command.cmd, command.args, {
        env: this.getIDEEnvironment(),
        timeout: 10000
      });

      if (result.code === 0 && result.stdout) {
        const extensions = result.stdout.split('\n');
        return extensions.some(ext => ext.includes('anthropic.claude-code'));
      }

      return false;
    } catch (error) {
      return false;
    }
  }

  /**
   * 获取扩展列表命令
   * @param {string} ideType - IDE类型
   * @returns {Object|null} 命令对象
   */
  getExtensionListCommand(ideType) {
    const commands = {
      [SUPPORTED_IDES.VSCODE]: { cmd: 'code', args: ['--list-extensions', '--show-versions'] },
      [SUPPORTED_IDES.CURSOR]: { cmd: 'cursor', args: ['--list-extensions', '--show-versions'] },
      [SUPPORTED_IDES.WINDSURF]: { cmd: 'windsurf', args: ['--list-extensions', '--show-versions'] }
    };

    return commands[ideType] || null;
  }

  /**
   * 检查JetBrains扩展
   * @param {string} ideType - IDE类型
   * @returns {Promise<boolean>} 是否已安装
   */
  async checkJetBrainsExtension(ideType) {
    // 实现JetBrains扩展检测逻辑
    return false;
  }

  /**
   * 获取IDE环境变量
   * @original: fD1()函数 (L16891)
   * @returns {Object} 环境变量对象
   */
  getIDEEnvironment() {
    return {
      ...process.env,
      // 禁用各种通知和缓存
      NPM_CONFIG_CACHE: '/dev/null',
      NPM_CONFIG_AUDIT: 'false',
      NPM_CONFIG_UPDATE_NOTIFIER: 'false',
      NPM_CONFIG_FUND: 'false',
      NO_UPDATE_NOTIFIER: '1',
      NODE_DISABLE_COLORS: '1'
    };
  }

  /**
   * 从缓存获取值
   * @param {string} key - 缓存键
   * @returns {*} 缓存值
   */
  getFromCache(key) {
    const cached = this.detectionCache.get(key);
    
    if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
      return cached.value;
    }

    return null;
  }

  /**
   * 设置缓存值
   * @param {string} key - 缓存键
   * @param {*} value - 缓存值
   */
  setCache(key, value) {
    this.detectionCache.set(key, {
      value,
      timestamp: Date.now()
    });
  }

  /**
   * 清除缓存
   */
  clearCache() {
    this.detectionCache.clear();
  }
}

/**
 * IDE扩展安装器类
 */
export class IDEExtensionInstaller {
  constructor() {
    this.installationQueue = [];
    this.isInstalling = false;
  }

  /**
   * 安装IDE扩展
   * @original: ux6()函数 (L13749)
   * @param {string} ideType - IDE类型
   * @returns {Promise<Object>} 安装结果
   */
  async installExtension(ideType) {
    try {
      const installResult = await this.performInstallation(ideType);
      
      logTelemetryEvent('tengu_ext_installed', {});
      
      // 更新配置
      const config = getConfig();
      if (!config.diffTool) {
        await setConfig('diffTool', ideType);
      }

      return {
        installed: true,
        error: null,
        installedVersion: installResult.version,
        ideType
      };

    } catch (error) {
      logTelemetryEvent('tengu_ext_install_error', {});
      
      const errorMessage = error instanceof Error ? error.message : String(error);
      logger.error('IDE扩展安装失败', { ideType, error: errorMessage });

      return {
        installed: false,
        error: errorMessage,
        installedVersion: null,
        ideType
      };
    }
  }

  /**
   * 执行安装
   * @param {string} ideType - IDE类型
   * @returns {Promise<Object>} 安装结果
   */
  async performInstallation(ideType) {
    switch (ideType) {
      case SUPPORTED_IDES.VSCODE:
      case SUPPORTED_IDES.CURSOR:
      case SUPPORTED_IDES.WINDSURF:
        return await this.installVSCodeExtension(ideType);
      
      default:
        if (this.isJetBrainsIDE(ideType)) {
          return await this.installJetBrainsPlugin(ideType);
        }
        throw new Error(`不支持的IDE类型: ${ideType}`);
    }
  }

  /**
   * 安装VSCode类扩展
   * @original: VSCode扩展安装逻辑 (L13862-13890)
   * @param {string} ideType - IDE类型
   * @returns {Promise<Object>} 安装结果
   */
  async installVSCodeExtension(ideType) {
    const command = this.getInstallCommand(ideType);
    
    if (!command) {
      throw new Error(`无法获取 ${ideType} 的安装命令`);
    }

    // 检查是否从市场安装
    const installFromMarketplace = await this.shouldInstallFromMarketplace(ideType);
    
    if (installFromMarketplace) {
      // 从市场安装
      const currentVersion = await this.getCurrentExtensionVersion(ideType);
      const requiredVersion = this.getRequiredExtensionVersion();
      
      if (!currentVersion || this.isVersionLower(currentVersion, requiredVersion)) {
        await new Promise(resolve => setTimeout(resolve, 500));
        
        const result = await executeCommand(command.cmd, [
          '--force',
          '--install-extension',
          'anthropic.claude-code'
        ], {
          env: this.getIDEEnvironment()
        });

        if (result.code !== 0) {
          throw new Error(`${result.code}: ${result.error} ${result.stderr}`);
        }
      }
    } else {
      // 从本地VSIX文件安装
      const [vsixPath, tempPath] = await this.downloadVSIXFile();
      
      try {
        const result = await executeCommand(command.cmd, [
          '--force',
          '--install-extension',
          tempPath
        ], {
          env: this.getIDEEnvironment()
        });

        if (result.code !== 0) {
          throw new Error(`${result.code}: ${result.error} ${result.stderr}`);
        }
      } finally {
        // 清理临时文件
        this.cleanupTempFile(tempPath);
      }
    }

    return {
      version: await this.getCurrentExtensionVersion(ideType) || 'unknown'
    };
  }

  /**
   * 安装JetBrains插件
   * @original: JetBrains插件安装逻辑 (L13267-13284)
   * @param {string} ideType - IDE类型
   * @returns {Promise<Object>} 安装结果
   */
  async installJetBrainsPlugin(ideType) {
    // 实现JetBrains插件安装逻辑
    throw new Error('JetBrains插件安装暂未实现');
  }

  /**
   * 获取安装命令
   * @param {string} ideType - IDE类型
   * @returns {Object|null} 命令对象
   */
  getInstallCommand(ideType) {
    const commands = {
      [SUPPORTED_IDES.VSCODE]: { cmd: 'code' },
      [SUPPORTED_IDES.CURSOR]: { cmd: 'cursor' },
      [SUPPORTED_IDES.WINDSURF]: { cmd: 'windsurf' }
    };

    return commands[ideType] || null;
  }

  /**
   * 检查是否应该从市场安装
   * @param {string} ideType - IDE类型
   * @returns {Promise<boolean>} 是否从市场安装
   */
  async shouldInstallFromMarketplace(ideType) {
    // 实现市场安装决策逻辑
    return true;
  }

  /**
   * 获取当前扩展版本
   * @param {string} ideType - IDE类型
   * @returns {Promise<string|null>} 当前版本
   */
  async getCurrentExtensionVersion(ideType) {
    // 实现当前扩展版本获取逻辑
    return null;
  }

  /**
   * 获取所需扩展版本
   * @returns {string} 所需版本
   */
  getRequiredExtensionVersion() {
    return APP_INFO.VERSION;
  }

  /**
   * 比较版本
   * @param {string} version1 - 版本1
   * @param {string} version2 - 版本2
   * @returns {boolean} version1是否低于version2
   */
  isVersionLower(version1, version2) {
    // 实现版本比较逻辑
    return version1 < version2;
  }

  /**
   * 下载VSIX文件
   * @returns {Promise<Array>} [原始路径, 临时路径]
   */
  async downloadVSIXFile() {
    // 实现VSIX文件下载逻辑
    return ['', ''];
  }

  /**
   * 清理临时文件
   * @param {string} filePath - 文件路径
   */
  cleanupTempFile(filePath) {
    try {
      if (existsSync(filePath)) {
        const fs = require('fs');
        fs.unlinkSync(filePath);
      }
    } catch (error) {
      logger.warn('清理临时文件失败', { filePath, error: error.message });
    }
  }

  /**
   * 获取IDE环境变量
   * @returns {Object} 环境变量
   */
  getIDEEnvironment() {
    return {
      ...process.env,
      NPM_CONFIG_CACHE: '/dev/null',
      NPM_CONFIG_AUDIT: 'false',
      NPM_CONFIG_UPDATE_NOTIFIER: 'false',
      NPM_CONFIG_FUND: 'false'
    };
  }

  /**
   * 检查是否为JetBrains IDE
   * @param {string} ideType - IDE类型
   * @returns {boolean} 是否为JetBrains IDE
   */
  isJetBrainsIDE(ideType) {
    const jetbrainsIDEs = [
      SUPPORTED_IDES.INTELLIJ,
      SUPPORTED_IDES.PYCHARM,
      SUPPORTED_IDES.WEBSTORM,
      SUPPORTED_IDES.PHPSTORM,
      SUPPORTED_IDES.RUBYMINE,
      SUPPORTED_IDES.CLION,
      SUPPORTED_IDES.GOLAND,
      SUPPORTED_IDES.RIDER,
      SUPPORTED_IDES.ANDROID_STUDIO
    ];
    
    return jetbrainsIDEs.includes(ideType);
  }
}

/**
 * IDE配置管理器类
 */
export class IDEConfigManager {
  /**
   * 获取IDE配置目录
   * @param {string} ideType - IDE类型
   * @returns {string|null} 配置目录路径
   */
  getIDEConfigDirectory(ideType) {
    const userHome = homedir();
    
    const configPaths = {
      [SUPPORTED_IDES.VSCODE]: join(userHome, '.vscode'),
      [SUPPORTED_IDES.CURSOR]: join(userHome, '.cursor'),
      [SUPPORTED_IDES.WINDSURF]: join(userHome, '.windsurf')
    };

    return configPaths[ideType] || null;
  }

  /**
   * 读取IDE配置
   * @param {string} ideType - IDE类型
   * @param {string} configFile - 配置文件名
   * @returns {Object|null} 配置对象
   */
  readIDEConfig(ideType, configFile) {
    try {
      const configDir = this.getIDEConfigDirectory(ideType);
      if (!configDir) return null;

      const configPath = join(configDir, configFile);
      if (!existsSync(configPath)) return null;

      const content = readFileSync(configPath, 'utf8');
      return JSON.parse(content);
    } catch (error) {
      logger.warn('读取IDE配置失败', { ideType, configFile, error: error.message });
      return null;
    }
  }

  /**
   * 写入IDE配置
   * @param {string} ideType - IDE类型
   * @param {string} configFile - 配置文件名
   * @param {Object} config - 配置对象
   * @returns {boolean} 是否写入成功
   */
  writeIDEConfig(ideType, configFile, config) {
    try {
      const configDir = this.getIDEConfigDirectory(ideType);
      if (!configDir) return false;

      const configPath = join(configDir, configFile);
      
      // 确保目录存在
      const fs = require('fs');
      if (!existsSync(configDir)) {
        fs.mkdirSync(configDir, { recursive: true });
      }

      const content = JSON.stringify(config, null, 2);
      writeFileSync(configPath, content, 'utf8');
      
      return true;
    } catch (error) {
      logger.error('写入IDE配置失败', { ideType, configFile, error: error.message });
      return false;
    }
  }
}

/**
 * 全局IDE检测器实例
 */
export const globalIDEDetector = new IDEDetector();

/**
 * 全局IDE扩展安装器实例
 */
export const globalIDEExtensionInstaller = new IDEExtensionInstaller();

/**
 * 全局IDE配置管理器实例
 */
export const globalIDEConfigManager = new IDEConfigManager();

/**
 * 便捷函数：检测已安装的IDE
 * @returns {Promise<Array>} 已安装的IDE列表
 */
export async function detectInstalledIDEs() {
  return globalIDEDetector.detectInstalledIDEs();
}

/**
 * 便捷函数：安装IDE扩展
 * @param {string} ideType - IDE类型
 * @returns {Promise<Object>} 安装结果
 */
export async function installIDEExtension(ideType) {
  return globalIDEExtensionInstaller.installExtension(ideType);
}

/**
 * 便捷函数：检查IDE是否已安装
 * @param {string} ideType - IDE类型
 * @returns {Promise<boolean>} 是否已安装
 */
export async function isIDEInstalled(ideType) {
  return globalIDEDetector.isIDEInstalled(ideType);
}
