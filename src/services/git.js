/**
 * Git集成服务
 * 
 * 管理Git操作，包括分支管理、状态检查、远程仓库操作等
 * 
 * @original: Git相关代码 (L55831-56272, L4807-4834)
 */

import { logger } from '../utils/logger.js';
import { executeCommand } from '../utils/process.js';
import { existsSync } from 'fs';
import { join } from 'path';

/**
 * Git状态枚举
 */
export const GIT_STATUS = {
  CLEAN: 'clean',
  DIRTY: 'dirty',
  UNTRACKED: 'untracked',
  STAGED: 'staged',
  MODIFIED: 'modified',
  DELETED: 'deleted',
  RENAMED: 'renamed',
  COPIED: 'copied'
};

/**
 * Git操作类型
 */
export const GIT_OPERATIONS = {
  STATUS: 'status',
  ADD: 'add',
  COMMIT: 'commit',
  PUSH: 'push',
  PULL: 'pull',
  CHECKOUT: 'checkout',
  BRANCH: 'branch',
  MERGE: 'merge',
  STASH: 'stash',
  RESET: 'reset'
};

/**
 * Git管理器类
 */
export class GitManager {
  constructor(workingDirectory = process.cwd()) {
    this.workingDirectory = workingDirectory;
    this.gitPath = 'git';
  }

  /**
   * 检查是否为Git仓库
   * @returns {Promise<boolean>} 是否为Git仓库
   */
  async isGitRepository() {
    try {
      const result = await this.executeGitCommand(['rev-parse', '--git-dir']);
      return result.code === 0;
    } catch (error) {
      return false;
    }
  }

  /**
   * 获取Git状态
   * @original: VYA()函数 (L4807-4821)
   * @returns {Promise<Object>} Git状态信息
   */
  async getGitStatus() {
    try {
      const [
        isRepo,
        isClean,
        remoteUrl,
        currentBranch,
        hasUncommittedChanges,
        hasUnpushedCommits
      ] = await Promise.all([
        this.isGitRepository(),
        this.isWorkingDirectoryClean(),
        this.getRemoteUrl(),
        this.getCurrentBranch(),
        this.hasUncommittedChanges(),
        this.hasUnpushedCommits()
      ]);

      return {
        isRepository: isRepo,
        isClean,
        remoteUrl,
        currentBranch,
        hasUncommittedChanges,
        hasUnpushedCommits,
        workingDirectory: this.workingDirectory
      };

    } catch (error) {
      logger.error('获取Git状态失败', { error: error.message });
      throw error;
    }
  }

  /**
   * 检查工作目录是否干净
   * @original: bK1()函数检查逻辑
   * @returns {Promise<boolean>} 工作目录是否干净
   */
  async isWorkingDirectoryClean() {
    try {
      const result = await this.executeGitCommand(['status', '--porcelain']);
      return result.code === 0 && result.stdout.trim() === '';
    } catch (error) {
      return false;
    }
  }

  /**
   * 获取远程仓库URL
   * @original: x91()函数 (L55833-55841)
   * @returns {Promise<string|null>} 远程仓库URL
   */
  async getRemoteUrl() {
    try {
      const result = await this.executeGitCommand(['config', '--get', 'remote.origin.url']);
      
      if (result.code === 0 && result.stdout) {
        return result.stdout.trim();
      }

      return null;

    } catch (error) {
      logger.warn('获取远程仓库URL失败', { error: error.message });
      return null;
    }
  }

  /**
   * 解析仓库信息
   * @original: bM8()函数 (L55841-55857)
   * @param {string} remoteUrl - 远程仓库URL
   * @returns {string|null} 解析的仓库名称
   */
  parseRepositoryInfo(remoteUrl) {
    if (!remoteUrl) return null;

    const url = remoteUrl.trim();
    
    // GitHub URL模式
    const githubPatterns = [
      /github\.com[:/]([^/]+\/[^/.]+?)(\.git)?$/,
      /github\.com[:/]([^/]+\/[^/.]+)$/
    ];

    for (const pattern of githubPatterns) {
      const match = url.match(pattern);
      if (match && match[1]) {
        logger.debug('解析仓库信息', { repository: match[1], url });
        return match[1];
      }
    }

    // 通用格式解析
    const parts = url.split('/');
    if (parts.length === 2 && parts[0] && parts[1]) {
      const repoName = parts[1].replace(/\.git$/, '');
      return `${parts[0]}/${repoName}`;
    }

    logger.warn('无法解析仓库信息', { url });
    return null;
  }

  /**
   * 获取当前分支
   * @original: WT0()函数 (L56168-56173)
   * @returns {Promise<string|null>} 当前分支名
   */
  async getCurrentBranch() {
    try {
      const result = await this.executeGitCommand(['branch', '--show-current']);
      
      if (result.code === 0 && result.stdout) {
        return result.stdout.trim();
      }

      return null;

    } catch (error) {
      logger.warn('获取当前分支失败', { error: error.message });
      return null;
    }
  }

  /**
   * 检查是否有未提交的更改
   * @returns {Promise<boolean>} 是否有未提交的更改
   */
  async hasUncommittedChanges() {
    try {
      const result = await this.executeGitCommand(['diff', '--quiet']);
      return result.code !== 0;
    } catch (error) {
      return false;
    }
  }

  /**
   * 检查是否有未推送的提交
   * @returns {Promise<boolean>} 是否有未推送的提交
   */
  async hasUnpushedCommits() {
    try {
      const result = await this.executeGitCommand(['log', '@{u}..', '--oneline']);
      return result.code === 0 && result.stdout.trim() !== '';
    } catch (error) {
      return false;
    }
  }

  /**
   * 获取更改的文件列表
   * @original: xr1()函数相关逻辑
   * @returns {Promise<Object>} 更改的文件信息
   */
  async getChangedFiles() {
    try {
      const result = await this.executeGitCommand(['status', '--porcelain']);
      
      if (result.code !== 0) {
        throw new Error(`Git status命令失败: ${result.stderr}`);
      }

      const lines = result.stdout.trim().split('\n').filter(line => line.trim());
      const tracked = [];
      const untracked = [];

      for (const line of lines) {
        const status = line.substring(0, 2);
        const filePath = line.substring(3);

        if (status.includes('?')) {
          untracked.push(filePath);
        } else {
          tracked.push({
            path: filePath,
            status: this.parseFileStatus(status)
          });
        }
      }

      return {
        tracked,
        untracked,
        hasChanges: tracked.length > 0 || untracked.length > 0
      };

    } catch (error) {
      logger.error('获取更改文件列表失败', { error: error.message });
      throw error;
    }
  }

  /**
   * 解析文件状态
   * @param {string} statusCode - Git状态代码
   * @returns {string} 解析后的状态
   */
  parseFileStatus(statusCode) {
    const statusMap = {
      'M ': GIT_STATUS.MODIFIED,
      ' M': GIT_STATUS.MODIFIED,
      'MM': GIT_STATUS.MODIFIED,
      'A ': GIT_STATUS.STAGED,
      ' A': GIT_STATUS.STAGED,
      'D ': GIT_STATUS.DELETED,
      ' D': GIT_STATUS.DELETED,
      'R ': GIT_STATUS.RENAMED,
      ' R': GIT_STATUS.RENAMED,
      'C ': GIT_STATUS.COPIED,
      ' C': GIT_STATUS.COPIED,
      '??': GIT_STATUS.UNTRACKED
    };

    return statusMap[statusCode] || GIT_STATUS.MODIFIED;
  }

  /**
   * 切换分支
   * @original: nM8()函数 (L56147-56167)
   * @param {string} branchName - 分支名称
   * @param {boolean} createIfNotExists - 如果分支不存在是否创建
   * @returns {Promise<Object>} 切换结果
   */
  async checkoutBranch(branchName, createIfNotExists = false) {
    try {
      // 首先尝试切换到现有分支
      let result = await this.executeGitCommand(['checkout', '-b', branchName, `origin/${branchName}`]);
      
      if (result.code !== 0) {
        // 如果失败，尝试跟踪远程分支
        result = await this.executeGitCommand(['checkout', '--track', `origin/${branchName}`]);
        
        if (result.code !== 0) {
          if (createIfNotExists) {
            // 创建新分支
            result = await this.executeGitCommand(['checkout', '-b', branchName]);
          }
          
          if (result.code !== 0) {
            throw new Error(`切换分支失败 '${branchName}': ${result.stderr}`);
          }
        }
      }

      logger.info('成功切换分支', { branchName });
      
      return {
        success: true,
        branchName,
        message: `成功切换到分支 '${branchName}'`
      };

    } catch (error) {
      logger.error('切换分支失败', { branchName, error: error.message });
      throw error;
    }
  }

  /**
   * 暂存更改
   * @returns {Promise<Object>} 暂存结果
   */
  async stashChanges() {
    try {
      const result = await this.executeGitCommand(['stash', 'push', '-m', 'Claude Code auto-stash']);
      
      if (result.code !== 0) {
        throw new Error(`暂存更改失败: ${result.stderr}`);
      }

      logger.info('成功暂存更改');
      
      return {
        success: true,
        message: '更改已暂存'
      };

    } catch (error) {
      logger.error('暂存更改失败', { error: error.message });
      throw error;
    }
  }

  /**
   * 恢复暂存的更改
   * @returns {Promise<Object>} 恢复结果
   */
  async popStash() {
    try {
      const result = await this.executeGitCommand(['stash', 'pop']);
      
      if (result.code !== 0) {
        throw new Error(`恢复暂存失败: ${result.stderr}`);
      }

      logger.info('成功恢复暂存的更改');
      
      return {
        success: true,
        message: '暂存的更改已恢复'
      };

    } catch (error) {
      logger.error('恢复暂存失败', { error: error.message });
      throw error;
    }
  }

  /**
   * 获取提交历史
   * @param {number} limit - 限制数量
   * @returns {Promise<Array>} 提交历史
   */
  async getCommitHistory(limit = 10) {
    try {
      const result = await this.executeGitCommand([
        'log',
        `--max-count=${limit}`,
        '--pretty=format:%H|%an|%ae|%ad|%s',
        '--date=iso'
      ]);

      if (result.code !== 0) {
        throw new Error(`获取提交历史失败: ${result.stderr}`);
      }

      const commits = result.stdout.trim().split('\n')
        .filter(line => line.trim())
        .map(line => {
          const [hash, author, email, date, message] = line.split('|');
          return {
            hash,
            author,
            email,
            date: new Date(date),
            message
          };
        });

      return commits;

    } catch (error) {
      logger.error('获取提交历史失败', { error: error.message });
      throw error;
    }
  }

  /**
   * 执行Git命令
   * @param {Array} args - Git命令参数
   * @param {Object} options - 执行选项
   * @returns {Promise<Object>} 执行结果
   */
  async executeGitCommand(args, options = {}) {
    const defaultOptions = {
      cwd: this.workingDirectory,
      timeout: 30000,
      ...options
    };

    try {
      const result = await executeCommand(this.gitPath, args, defaultOptions);
      
      logger.debug('Git命令执行完成', { 
        args: args.join(' '), 
        code: result.code,
        cwd: this.workingDirectory
      });

      return result;

    } catch (error) {
      logger.error('Git命令执行失败', { 
        args: args.join(' '), 
        error: error.message,
        cwd: this.workingDirectory
      });
      throw error;
    }
  }
}

/**
 * GitHub集成管理器类
 */
export class GitHubManager {
  constructor() {
    this.ghPath = 'gh';
  }

  /**
   * 检查GitHub CLI是否可用
   * @returns {Promise<boolean>} GitHub CLI是否可用
   */
  async isGitHubCLIAvailable() {
    try {
      const result = await executeCommand(this.ghPath, ['--version'], { timeout: 5000 });
      return result.code === 0;
    } catch (error) {
      return false;
    }
  }

  /**
   * 获取仓库信息
   * @original: JYA()函数相关逻辑
   * @param {string} repository - 仓库名称 (owner/repo)
   * @returns {Promise<Object>} 仓库信息
   */
  async getRepositoryInfo(repository) {
    try {
      if (!(await this.isGitHubCLIAvailable())) {
        throw new Error('GitHub CLI不可用');
      }

      const result = await executeCommand(this.ghPath, [
        'api',
        `repos/${repository}`,
        '--jq',
        '.id,.name,.full_name,.private,.default_branch,.permissions'
      ]);

      if (result.code !== 0) {
        throw new Error(`获取仓库信息失败: ${result.stderr}`);
      }

      const lines = result.stdout.trim().split('\n');
      
      return {
        id: lines[0],
        name: lines[1],
        fullName: lines[2],
        isPrivate: lines[3] === 'true',
        defaultBranch: lines[4],
        permissions: JSON.parse(lines[5] || '{}')
      };

    } catch (error) {
      logger.error('获取GitHub仓库信息失败', { repository, error: error.message });
      throw error;
    }
  }

  /**
   * 检查仓库管理员权限
   * @param {string} repository - 仓库名称
   * @returns {Promise<boolean>} 是否有管理员权限
   */
  async hasAdminPermission(repository) {
    try {
      const result = await executeCommand(this.ghPath, [
        'api',
        `repos/${repository}`,
        '--jq',
        '.permissions.admin'
      ]);

      return result.code === 0 && result.stdout.trim() === 'true';

    } catch (error) {
      logger.warn('检查管理员权限失败', { repository, error: error.message });
      return false;
    }
  }

  /**
   * 检查工作流文件是否存在
   * @param {string} repository - 仓库名称
   * @param {string} workflowFile - 工作流文件名
   * @returns {Promise<boolean>} 工作流文件是否存在
   */
  async workflowExists(repository, workflowFile = 'claude.yml') {
    try {
      const result = await executeCommand(this.ghPath, [
        'api',
        `repos/${repository}/contents/.github/workflows/${workflowFile}`,
        '--jq',
        '.sha'
      ]);

      return result.code === 0;

    } catch (error) {
      return false;
    }
  }

  /**
   * 获取仓库密钥列表
   * @param {string} repository - 仓库名称
   * @returns {Promise<Array>} 密钥列表
   */
  async getRepositorySecrets(repository) {
    try {
      const result = await executeCommand(this.ghPath, [
        'secret',
        'list',
        '--app',
        'actions',
        '--repo',
        repository
      ]);

      if (result.code !== 0) {
        throw new Error(`获取仓库密钥失败: ${result.stderr}`);
      }

      const secrets = result.stdout.trim().split('\n')
        .filter(line => line.trim())
        .map(line => {
          const parts = line.split('\t');
          return {
            name: parts[0],
            updatedAt: parts[1] || null
          };
        });

      return secrets;

    } catch (error) {
      logger.error('获取仓库密钥失败', { repository, error: error.message });
      throw error;
    }
  }
}

/**
 * Git错误检测器类
 */
export class GitErrorDetector {
  /**
   * 检测Git相关错误
   * @original: YT0()函数 (L56277-56283)
   * @returns {Promise<Set>} 检测到的错误集合
   */
  async detectGitErrors() {
    const errors = new Set();

    try {
      const [isClean, hasAuth] = await Promise.all([
        this.checkWorkingDirectoryClean(),
        this.checkAuthentication()
      ]);

      if (!isClean) {
        errors.add('needsGitStash');
      }

      if (!hasAuth) {
        errors.add('needsAuth');
      }

      return errors;

    } catch (error) {
      logger.error('Git错误检测失败', { error: error.message });
      errors.add('gitDetectionFailed');
      return errors;
    }
  }

  /**
   * 检查工作目录是否干净
   * @returns {Promise<boolean>} 工作目录是否干净
   */
  async checkWorkingDirectoryClean() {
    const gitManager = new GitManager();
    return gitManager.isWorkingDirectoryClean();
  }

  /**
   * 检查认证状态
   * @returns {Promise<boolean>} 认证是否有效
   */
  async checkAuthentication() {
    // 这里需要实现认证检查逻辑
    // 可能涉及检查API token、OAuth状态等
    return true;
  }
}

/**
 * Git工作流管理器类
 */
export class GitWorkflowManager {
  constructor(gitManager, githubManager) {
    this.gitManager = gitManager || new GitManager();
    this.githubManager = githubManager || new GitHubManager();
  }

  /**
   * 准备Teleport操作
   * @original: pM8()函数 (L56132-56139)
   * @param {Object} options - Teleport选项
   * @returns {Promise<void>}
   */
  async prepareTeleportOperation(options = {}) {
    const isClean = await this.gitManager.isWorkingDirectoryClean();
    
    if (!isClean) {
      const errorMessage = 'Git工作目录不干净。请在使用--teleport前提交或暂存您的更改。';
      const formattedMessage = `错误: Git工作目录不干净。请在使用--teleport前提交或暂存您的更改。\n`;
      
      throw new Error(errorMessage);
    }
  }

  /**
   * 执行分支切换工作流
   * @original: aM8()函数 (L56174-56190)
   * @param {Array} messages - 消息数组
   * @param {string} targetBranch - 目标分支
   * @param {Object} options - 选项
   * @returns {Promise<Object>} 工作流结果
   */
  async executeBranchSwitchWorkflow(messages, targetBranch, options = {}) {
    try {
      const currentBranch = await this.gitManager.getCurrentBranch();
      logger.info('Teleport前的当前分支', { currentBranch });

      if (targetBranch) {
        await this.gitManager.checkoutBranch(targetBranch);
        const newBranch = await this.gitManager.getCurrentBranch();
        logger.info('切换分支后', { newBranch });
      }

      // 转换消息格式
      const convertedMessages = this.convertMessages(messages);
      logger.info('消息转换完成', { 
        convertedCount: convertedMessages.length,
        originalCount: messages.length 
      });

      const finalBranch = await this.gitManager.getCurrentBranch();
      
      return {
        messages: convertedMessages,
        branch: finalBranch,
        originalBranch: currentBranch
      };

    } catch (error) {
      logger.error('分支切换工作流失败', { error: error.message });
      throw error;
    }
  }

  /**
   * 转换消息格式
   * @original: IT0()函数 (L55630-55653)
   * @param {Array} messages - 原始消息数组
   * @returns {Array} 转换后的消息数组
   */
  convertMessages(messages) {
    return messages.flatMap(message => {
      switch (message.type) {
        case 'user':
          return [{
            type: 'user',
            content: message.content
          }];
        
        case 'assistant':
          return [{
            type: 'assistant',
            content: message.content
          }];
        
        default:
          logger.warn('未知消息类型', { type: message.type });
          return [];
      }
    });
  }
}

/**
 * 全局Git管理器实例
 */
export const globalGitManager = new GitManager();

/**
 * 全局GitHub管理器实例
 */
export const globalGitHubManager = new GitHubManager();

/**
 * 全局Git错误检测器实例
 */
export const globalGitErrorDetector = new GitErrorDetector();

/**
 * 全局Git工作流管理器实例
 */
export const globalGitWorkflowManager = new GitWorkflowManager();

/**
 * 便捷函数：获取Git状态
 * @returns {Promise<Object>} Git状态信息
 */
export async function getGitStatus() {
  return globalGitManager.getGitStatus();
}

/**
 * 便捷函数：检查工作目录是否干净
 * @returns {Promise<boolean>} 工作目录是否干净
 */
export async function isWorkingDirectoryClean() {
  return globalGitManager.isWorkingDirectoryClean();
}

/**
 * 便捷函数：获取更改的文件
 * @returns {Promise<Object>} 更改的文件信息
 */
export async function getChangedFiles() {
  return globalGitManager.getChangedFiles();
}

/**
 * 便捷函数：切换分支
 * @param {string} branchName - 分支名称
 * @param {boolean} createIfNotExists - 如果不存在是否创建
 * @returns {Promise<Object>} 切换结果
 */
export async function checkoutBranch(branchName, createIfNotExists = false) {
  return globalGitManager.checkoutBranch(branchName, createIfNotExists);
}
