/**
 * MCP (Model Context Protocol) 服务
 * 
 * 管理MCP服务器的连接、配置、工具调用等功能
 * 
 * @original: MCP相关代码 (L57687-57877, L54000-54200)
 */

import { logger } from '../utils/logger.js';
import { executeCommand } from '../utils/process.js';
import { readJSONFile, writeJSONFile } from '../utils/filesystem.js';

/**
 * MCP传输类型枚举
 */
export const MCP_TRANSPORT_TYPES = {
  STDIO: 'stdio',
  SSE: 'sse',
  HTTP: 'http'
};

/**
 * MCP配置作用域枚举
 */
export const MCP_CONFIG_SCOPES = {
  LOCAL: 'local',
  USER: 'user',
  PROJECT: 'project'
};

/**
 * MCP服务器状态枚举
 */
export const MCP_SERVER_STATUS = {
  DISCONNECTED: 'disconnected',
  CONNECTING: 'connecting',
  CONNECTED: 'connected',
  ERROR: 'error'
};

/**
 * MCP服务器类
 */
export class MCPServer {
  constructor(config) {
    this.name = config.name;
    this.command = config.command;
    this.args = config.args || [];
    this.env = config.env || {};
    this.transport = config.transport || MCP_TRANSPORT_TYPES.STDIO;
    this.scope = config.scope || MCP_CONFIG_SCOPES.LOCAL;
    this.enabled = config.enabled !== false;
    
    this.status = MCP_SERVER_STATUS.DISCONNECTED;
    this.process = null;
    this.tools = new Map();
    this.lastError = null;
    this.connectionAttempts = 0;
    this.maxConnectionAttempts = 3;
  }

  /**
   * 启动MCP服务器
   * @returns {Promise<boolean>} 是否启动成功
   */
  async start() {
    if (this.status === MCP_SERVER_STATUS.CONNECTED) {
      return true;
    }

    this.status = MCP_SERVER_STATUS.CONNECTING;
    this.connectionAttempts++;

    try {
      logger.debug('启动MCP服务器', { 
        name: this.name, 
        command: this.command,
        transport: this.transport 
      });

      switch (this.transport) {
        case MCP_TRANSPORT_TYPES.STDIO:
          return await this.startStdioServer();
        case MCP_TRANSPORT_TYPES.SSE:
          return await this.startSSEServer();
        case MCP_TRANSPORT_TYPES.HTTP:
          return await this.startHTTPServer();
        default:
          throw new Error(`Unsupported transport type: ${this.transport}`);
      }

    } catch (error) {
      this.status = MCP_SERVER_STATUS.ERROR;
      this.lastError = error.message;
      
      logger.error('MCP服务器启动失败', { 
        name: this.name, 
        error: error.message,
        attempts: this.connectionAttempts 
      });

      return false;
    }
  }

  /**
   * 启动STDIO传输的MCP服务器
   * @returns {Promise<boolean>} 是否启动成功
   */
  async startStdioServer() {
    try {
      const result = await executeCommand(this.command, this.args, {
        env: { ...process.env, ...this.env },
        stdio: ['pipe', 'pipe', 'pipe'],
        background: true
      });

      this.process = result.process;
      this.status = MCP_SERVER_STATUS.CONNECTED;

      // 设置进程事件监听器
      this.setupProcessListeners();

      // 初始化MCP协议
      await this.initializeMCPProtocol();

      logger.info('STDIO MCP服务器已启动', { name: this.name });
      return true;

    } catch (error) {
      throw new Error(`Failed to start STDIO server: ${error.message}`);
    }
  }

  /**
   * 启动SSE传输的MCP服务器
   * @returns {Promise<boolean>} 是否启动成功
   */
  async startSSEServer() {
    // 实现SSE传输逻辑
    logger.warn('SSE传输暂未实现', { name: this.name });
    return false;
  }

  /**
   * 启动HTTP传输的MCP服务器
   * @returns {Promise<boolean>} 是否启动成功
   */
  async startHTTPServer() {
    // 实现HTTP传输逻辑
    logger.warn('HTTP传输暂未实现', { name: this.name });
    return false;
  }

  /**
   * 设置进程事件监听器
   */
  setupProcessListeners() {
    if (!this.process) return;

    this.process.on('exit', (code) => {
      this.status = MCP_SERVER_STATUS.DISCONNECTED;
      logger.warn('MCP服务器进程退出', { name: this.name, code });
    });

    this.process.on('error', (error) => {
      this.status = MCP_SERVER_STATUS.ERROR;
      this.lastError = error.message;
      logger.error('MCP服务器进程错误', { name: this.name, error: error.message });
    });
  }

  /**
   * 初始化MCP协议
   * @returns {Promise<void>}
   */
  async initializeMCPProtocol() {
    // 发送初始化消息
    const initMessage = {
      jsonrpc: '2.0',
      id: 1,
      method: 'initialize',
      params: {
        protocolVersion: '2024-11-05',
        capabilities: {
          tools: {}
        },
        clientInfo: {
          name: 'claude-code',
          version: '1.0.72'
        }
      }
    };

    await this.sendMessage(initMessage);
    
    // 等待初始化响应
    // 这里需要实现消息接收和处理逻辑
  }

  /**
   * 发送消息到MCP服务器
   * @param {Object} message - 消息对象
   * @returns {Promise<void>}
   */
  async sendMessage(message) {
    if (!this.process || this.status !== MCP_SERVER_STATUS.CONNECTED) {
      throw new Error(`MCP server ${this.name} is not connected`);
    }

    const messageStr = JSON.stringify(message) + '\n';
    
    return new Promise((resolve, reject) => {
      this.process.stdin.write(messageStr, (error) => {
        if (error) {
          reject(error);
        } else {
          resolve();
        }
      });
    });
  }

  /**
   * 停止MCP服务器
   * @returns {Promise<void>}
   */
  async stop() {
    if (this.process) {
      this.process.kill();
      this.process = null;
    }
    
    this.status = MCP_SERVER_STATUS.DISCONNECTED;
    this.tools.clear();
    
    logger.info('MCP服务器已停止', { name: this.name });
  }

  /**
   * 获取服务器信息
   * @returns {Object} 服务器信息
   */
  getInfo() {
    return {
      name: this.name,
      command: this.command,
      args: this.args,
      transport: this.transport,
      scope: this.scope,
      status: this.status,
      enabled: this.enabled,
      toolCount: this.tools.size,
      lastError: this.lastError,
      connectionAttempts: this.connectionAttempts
    };
  }
}

/**
 * MCP管理器类
 */
export class MCPManager {
  constructor() {
    this.servers = new Map();
    this.configPaths = new Map();
    this.strictMode = false;
  }

  /**
   * 添加MCP服务器
   * @original: MCP服务器添加逻辑 (L57698-57702)
   * @param {string} name - 服务器名称
   * @param {string} commandOrUrl - 命令或URL
   * @param {Array} args - 参数
   * @param {Object} options - 选项
   * @returns {Promise<boolean>} 是否添加成功
   */
  async addServer(name, commandOrUrl, args = [], options = {}) {
    if (!name) {
      throw new Error('Server name is required');
    }

    if (!commandOrUrl) {
      throw new Error('Command is required when server name is provided');
    }

    const scope = this.parseScope(options.scope || 'local');
    const transport = this.parseTransport(options.transport || 'stdio');

    const serverConfig = {
      name,
      command: commandOrUrl,
      args,
      transport,
      scope,
      env: this.parseEnvVars(options.env || []),
      headers: this.parseHeaders(options.headers || []),
      enabled: true
    };

    // 验证配置
    const validation = this.validateServerConfig(serverConfig);
    if (!validation.valid) {
      throw new Error(`Invalid server configuration: ${validation.errors.join(', ')}`);
    }

    // 创建服务器实例
    const server = new MCPServer(serverConfig);
    this.servers.set(name, server);

    // 保存配置
    await this.saveServerConfig(serverConfig);

    logger.info('MCP服务器已添加', { name, transport, scope });
    
    return true;
  }

  /**
   * 移除MCP服务器
   * @original: MCP服务器移除逻辑 (L57778-57783)
   * @param {string} name - 服务器名称
   * @param {Object} options - 选项
   * @returns {Promise<boolean>} 是否移除成功
   */
  async removeServer(name, options = {}) {
    const server = this.servers.get(name);
    
    if (!server) {
      throw new Error(`Server "${name}" not found`);
    }

    // 停止服务器
    await server.stop();
    
    // 从内存中移除
    this.servers.delete(name);

    // 从配置文件中移除
    await this.removeServerConfig(name, options.scope);

    logger.info('MCP服务器已移除', { name });
    
    return true;
  }

  /**
   * 列出所有MCP服务器
   * @original: MCP服务器列表逻辑 (L57790-57793)
   * @returns {Array} 服务器列表
   */
  listServers() {
    const servers = Array.from(this.servers.values());
    
    return servers.map(server => server.getInfo());
  }

  /**
   * 检查MCP服务器健康状态
   * @returns {Promise<Object>} 健康检查结果
   */
  async checkServerHealth() {
    const results = {};
    
    for (const [name, server] of this.servers) {
      try {
        if (server.status === MCP_SERVER_STATUS.CONNECTED) {
          // 发送ping消息检查连接
          await server.sendMessage({
            jsonrpc: '2.0',
            id: Date.now(),
            method: 'ping'
          });
          
          results[name] = {
            status: 'healthy',
            uptime: Date.now() - server.startTime
          };
        } else {
          results[name] = {
            status: 'disconnected',
            lastError: server.lastError
          };
        }
      } catch (error) {
        results[name] = {
          status: 'error',
          error: error.message
        };
      }
    }

    return results;
  }

  /**
   * 解析作用域
   * @param {string} scope - 作用域字符串
   * @returns {string} 解析后的作用域
   */
  parseScope(scope) {
    if (!Object.values(MCP_CONFIG_SCOPES).includes(scope)) {
      throw new Error(`Invalid scope: ${scope}. Must be one of: ${Object.values(MCP_CONFIG_SCOPES).join(', ')}`);
    }
    return scope;
  }

  /**
   * 解析传输类型
   * @param {string} transport - 传输类型字符串
   * @returns {string} 解析后的传输类型
   */
  parseTransport(transport) {
    if (!Object.values(MCP_TRANSPORT_TYPES).includes(transport)) {
      throw new Error(`Invalid transport: ${transport}. Must be one of: ${Object.values(MCP_TRANSPORT_TYPES).join(', ')}`);
    }
    return transport;
  }

  /**
   * 解析环境变量
   * @param {Array} envArray - 环境变量数组
   * @returns {Object} 环境变量对象
   */
  parseEnvVars(envArray) {
    const env = {};
    
    for (const envVar of envArray) {
      const [key, ...valueParts] = envVar.split('=');
      if (key && valueParts.length > 0) {
        env[key] = valueParts.join('=');
      }
    }
    
    return env;
  }

  /**
   * 解析HTTP头
   * @param {Array} headerArray - HTTP头数组
   * @returns {Object} HTTP头对象
   */
  parseHeaders(headerArray) {
    const headers = {};
    
    for (const header of headerArray) {
      const [key, ...valueParts] = header.split(':');
      if (key && valueParts.length > 0) {
        headers[key.trim()] = valueParts.join(':').trim();
      }
    }
    
    return headers;
  }

  /**
   * 验证服务器配置
   * @param {Object} config - 服务器配置
   * @returns {Object} 验证结果
   */
  validateServerConfig(config) {
    const errors = [];

    if (!config.name || typeof config.name !== 'string') {
      errors.push('Server name is required and must be a string');
    }

    if (!config.command || typeof config.command !== 'string') {
      errors.push('Server command is required and must be a string');
    }

    if (!Object.values(MCP_TRANSPORT_TYPES).includes(config.transport)) {
      errors.push(`Invalid transport type: ${config.transport}`);
    }

    if (!Object.values(MCP_CONFIG_SCOPES).includes(config.scope)) {
      errors.push(`Invalid scope: ${config.scope}`);
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  /**
   * 保存服务器配置
   * @param {Object} config - 服务器配置
   * @returns {Promise<void>}
   */
  async saveServerConfig(config) {
    try {
      const configPath = this.getConfigPath(config.scope);
      let existingConfig = {};
      
      try {
        existingConfig = await readJSONFile(configPath);
      } catch (error) {
        // 配置文件不存在，使用空对象
      }

      if (!existingConfig.mcpServers) {
        existingConfig.mcpServers = {};
      }

      existingConfig.mcpServers[config.name] = {
        command: config.command,
        args: config.args,
        env: config.env,
        transport: config.transport
      };

      await writeJSONFile(configPath, existingConfig);
      
      logger.debug('MCP服务器配置已保存', { 
        name: config.name, 
        scope: config.scope,
        configPath 
      });

    } catch (error) {
      logger.error('保存MCP服务器配置失败', { 
        name: config.name, 
        error: error.message 
      });
      throw error;
    }
  }

  /**
   * 移除服务器配置
   * @param {string} name - 服务器名称
   * @param {string} scope - 配置作用域
   * @returns {Promise<void>}
   */
  async removeServerConfig(name, scope) {
    try {
      const configPath = this.getConfigPath(scope);
      const existingConfig = await readJSONFile(configPath);

      if (existingConfig.mcpServers && existingConfig.mcpServers[name]) {
        delete existingConfig.mcpServers[name];
        await writeJSONFile(configPath, existingConfig);
        
        logger.debug('MCP服务器配置已移除', { name, scope, configPath });
      }

    } catch (error) {
      logger.error('移除MCP服务器配置失败', { name, error: error.message });
      throw error;
    }
  }

  /**
   * 获取配置文件路径
   * @param {string} scope - 配置作用域
   * @returns {string} 配置文件路径
   */
  getConfigPath(scope) {
    const path = require('path');
    const os = require('os');

    switch (scope) {
      case MCP_CONFIG_SCOPES.LOCAL:
        return path.join(process.cwd(), '.mcp.json');
      case MCP_CONFIG_SCOPES.USER:
        return path.join(os.homedir(), '.claude', 'mcp.json');
      case MCP_CONFIG_SCOPES.PROJECT:
        return path.join(process.cwd(), '.claude', 'mcp.json');
      default:
        throw new Error(`Invalid scope: ${scope}`);
    }
  }

  /**
   * 加载所有MCP配置
   * @returns {Promise<Object>} 加载的配置
   */
  async loadAllConfigs() {
    const configs = {};
    
    for (const scope of Object.values(MCP_CONFIG_SCOPES)) {
      try {
        const configPath = this.getConfigPath(scope);
        const config = await readJSONFile(configPath);
        
        if (config.mcpServers) {
          configs[scope] = config.mcpServers;
        }
      } catch (error) {
        // 配置文件不存在或读取失败，跳过
        logger.debug('MCP配置文件读取失败', { scope, error: error.message });
      }
    }

    return configs;
  }

  /**
   * 从Claude Desktop导入配置
   * @original: Claude Desktop导入逻辑 (L57844-57854)
   * @param {string} scope - 目标作用域
   * @returns {Promise<Object>} 导入结果
   */
  async importFromClaudeDesktop(scope = MCP_CONFIG_SCOPES.LOCAL) {
    try {
      const desktopConfig = await this.readClaudeDesktopConfig();
      
      if (!desktopConfig || Object.keys(desktopConfig).length === 0) {
        return {
          success: false,
          message: 'No MCP servers found in Claude Desktop configuration or configuration file does not exist'
        };
      }

      let importedCount = 0;
      
      for (const [name, config] of Object.entries(desktopConfig)) {
        try {
          await this.addServer(name, config.command, config.args || [], {
            scope,
            transport: config.transport || MCP_TRANSPORT_TYPES.STDIO,
            env: config.env || []
          });
          
          importedCount++;
        } catch (error) {
          logger.warn('导入MCP服务器失败', { name, error: error.message });
        }
      }

      return {
        success: true,
        importedCount,
        totalFound: Object.keys(desktopConfig).length
      };

    } catch (error) {
      logger.error('从Claude Desktop导入配置失败', { error: error.message });
      
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 读取Claude Desktop配置
   * @returns {Promise<Object>} Claude Desktop配置
   */
  async readClaudeDesktopConfig() {
    const path = require('path');
    const os = require('os');

    const configPaths = [];
    
    // macOS路径
    if (process.platform === 'darwin') {
      configPaths.push(
        path.join(os.homedir(), 'Library', 'Application Support', 'Claude', 'claude_desktop_config.json')
      );
    }
    
    // Windows路径
    if (process.platform === 'win32') {
      configPaths.push(
        path.join(os.homedir(), 'AppData', 'Roaming', 'Claude', 'claude_desktop_config.json')
      );
    }
    
    // Linux路径
    if (process.platform === 'linux') {
      configPaths.push(
        path.join(os.homedir(), '.config', 'Claude', 'claude_desktop_config.json')
      );
    }

    for (const configPath of configPaths) {
      try {
        const config = await readJSONFile(configPath);
        return config.mcpServers || {};
      } catch (error) {
        // 继续尝试下一个路径
      }
    }

    return {};
  }

  /**
   * 启动所有启用的服务器
   * @returns {Promise<Object>} 启动结果
   */
  async startAllServers() {
    const results = {};
    
    for (const [name, server] of this.servers) {
      if (server.enabled) {
        try {
          const success = await server.start();
          results[name] = { success, status: server.status };
        } catch (error) {
          results[name] = { success: false, error: error.message };
        }
      } else {
        results[name] = { success: false, reason: 'disabled' };
      }
    }

    return results;
  }

  /**
   * 停止所有服务器
   * @returns {Promise<void>}
   */
  async stopAllServers() {
    const stopPromises = Array.from(this.servers.values()).map(server => server.stop());
    await Promise.all(stopPromises);
    
    logger.info('所有MCP服务器已停止');
  }

  /**
   * 获取服务器
   * @param {string} name - 服务器名称
   * @returns {MCPServer|null} 服务器实例
   */
  getServer(name) {
    return this.servers.get(name) || null;
  }

  /**
   * 设置严格模式
   * @param {boolean} strict - 是否启用严格模式
   */
  setStrictMode(strict) {
    this.strictMode = strict;
    logger.debug('MCP严格模式', { enabled: strict });
  }
}

/**
 * 全局MCP管理器实例
 */
export const globalMCPManager = new MCPManager();

/**
 * 便捷函数：添加MCP服务器
 * @param {string} name - 服务器名称
 * @param {string} command - 命令
 * @param {Array} args - 参数
 * @param {Object} options - 选项
 * @returns {Promise<boolean>} 是否添加成功
 */
export async function addMCPServer(name, command, args = [], options = {}) {
  return globalMCPManager.addServer(name, command, args, options);
}

/**
 * 便捷函数：移除MCP服务器
 * @param {string} name - 服务器名称
 * @param {Object} options - 选项
 * @returns {Promise<boolean>} 是否移除成功
 */
export async function removeMCPServer(name, options = {}) {
  return globalMCPManager.removeServer(name, options);
}

/**
 * 便捷函数：列出MCP服务器
 * @returns {Array} 服务器列表
 */
export function listMCPServers() {
  return globalMCPManager.listServers();
}
