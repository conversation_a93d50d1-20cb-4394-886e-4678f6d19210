/**
 * 终端和进程管理服务
 * 
 * 管理Claude Code的终端界面、进程控制、输入输出处理等
 * 
 * @original: 终端相关代码 (L12324-12456, L13000-13200, L54432-54500)
 */

import { logger } from '../utils/logger.js';
import { executeCommand } from '../utils/process.js';

/**
 * 终端状态枚举
 */
export const TERMINAL_STATUS = {
  IDLE: 'idle',
  BUSY: 'busy',
  ERROR: 'error',
  CLOSED: 'closed'
};

/**
 * 输入模式枚举
 */
export const INPUT_MODES = {
  PROMPT: 'prompt',
  COMMAND: 'command',
  MULTILINE: 'multiline'
};

/**
 * 终端管理器类
 */
export class TerminalManager {
  constructor(options = {}) {
    this.status = TERMINAL_STATUS.IDLE;
    this.inputMode = INPUT_MODES.PROMPT;
    this.currentInput = '';
    this.inputHistory = [];
    this.historyIndex = -1;
    this.maxHistorySize = options.maxHistorySize || 1000;
    
    this.isRawMode = false;
    this.hasFocus = true;
    this.isTypingWithoutFocus = false;
    
    this.eventHandlers = new Map();
    this.keyBindings = new Map();
    
    this.initializeKeyBindings();
  }

  /**
   * 初始化键盘绑定
   * @original: 键盘绑定初始化逻辑 (L12324-12456)
   */
  initializeKeyBindings() {
    // 基础导航键
    this.keyBindings.set('up', this.handleUpArrow.bind(this));
    this.keyBindings.set('down', this.handleDownArrow.bind(this));
    this.keyBindings.set('left', this.handleLeftArrow.bind(this));
    this.keyBindings.set('right', this.handleRightArrow.bind(this));
    
    // 编辑键
    this.keyBindings.set('return', this.handleEnter.bind(this));
    this.keyBindings.set('backspace', this.handleBackspace.bind(this));
    this.keyBindings.set('delete', this.handleDelete.bind(this));
    this.keyBindings.set('tab', this.handleTab.bind(this));
    
    // 控制键组合
    this.keyBindings.set('ctrl+c', this.handleCtrlC.bind(this));
    this.keyBindings.set('ctrl+d', this.handleCtrlD.bind(this));
    this.keyBindings.set('ctrl+l', this.handleCtrlL.bind(this));
    this.keyBindings.set('ctrl+r', this.handleCtrlR.bind(this));
    this.keyBindings.set('ctrl+u', this.handleCtrlU.bind(this));
    this.keyBindings.set('ctrl+k', this.handleCtrlK.bind(this));
    
    // 特殊键组合
    this.keyBindings.set('escape', this.handleEscape.bind(this));
    this.keyBindings.set('cmd+escape', this.handleCmdEscape.bind(this));
    
    logger.debug('键盘绑定已初始化', { bindingCount: this.keyBindings.size });
  }

  /**
   * 处理键盘输入
   * @original: 键盘输入处理逻辑 (L54432-54500)
   * @param {string} key - 按键
   * @param {Object} input - 输入对象
   * @returns {Promise<boolean>} 是否处理了按键
   */
  async handleKeyInput(key, input) {
    logger.debug('处理键盘输入', { key, inputLength: input.sequence?.length || 0 });

    // 检查是否有绑定的处理器
    const handler = this.keyBindings.get(key);
    if (handler) {
      return await handler(input);
    }

    // 处理普通字符输入
    if (this.isPrintableCharacter(key)) {
      return this.handleCharacterInput(key, input);
    }

    return false;
  }

  /**
   * 处理上箭头键
   * @param {Object} input - 输入对象
   * @returns {Promise<boolean>} 是否处理成功
   */
  async handleUpArrow(input) {
    if (this.inputHistory.length === 0) return false;

    if (this.historyIndex === -1) {
      this.historyIndex = this.inputHistory.length - 1;
    } else if (this.historyIndex > 0) {
      this.historyIndex--;
    }

    this.currentInput = this.inputHistory[this.historyIndex];
    this.emitEvent('inputChanged', { input: this.currentInput });
    
    return true;
  }

  /**
   * 处理下箭头键
   * @param {Object} input - 输入对象
   * @returns {Promise<boolean>} 是否处理成功
   */
  async handleDownArrow(input) {
    if (this.inputHistory.length === 0) return false;

    if (this.historyIndex === -1) {
      return false;
    } else if (this.historyIndex < this.inputHistory.length - 1) {
      this.historyIndex++;
      this.currentInput = this.inputHistory[this.historyIndex];
    } else {
      this.historyIndex = -1;
      this.currentInput = '';
    }

    this.emitEvent('inputChanged', { input: this.currentInput });
    
    return true;
  }

  /**
   * 处理左箭头键
   * @param {Object} input - 输入对象
   * @returns {Promise<boolean>} 是否处理成功
   */
  async handleLeftArrow(input) {
    // 实现光标左移逻辑
    this.emitEvent('cursorMove', { direction: 'left' });
    return true;
  }

  /**
   * 处理右箭头键
   * @param {Object} input - 输入对象
   * @returns {Promise<boolean>} 是否处理成功
   */
  async handleRightArrow(input) {
    // 实现光标右移逻辑
    this.emitEvent('cursorMove', { direction: 'right' });
    return true;
  }

  /**
   * 处理回车键
   * @param {Object} input - 输入对象
   * @returns {Promise<boolean>} 是否处理成功
   */
  async handleEnter(input) {
    if (this.currentInput.trim()) {
      // 添加到历史记录
      this.addToHistory(this.currentInput);
      
      // 发送输入事件
      this.emitEvent('inputSubmitted', { 
        input: this.currentInput,
        mode: this.inputMode 
      });
      
      // 清空当前输入
      this.currentInput = '';
      this.historyIndex = -1;
    }
    
    return true;
  }

  /**
   * 处理退格键
   * @param {Object} input - 输入对象
   * @returns {Promise<boolean>} 是否处理成功
   */
  async handleBackspace(input) {
    if (this.currentInput.length > 0) {
      this.currentInput = this.currentInput.slice(0, -1);
      this.emitEvent('inputChanged', { input: this.currentInput });
    }
    
    return true;
  }

  /**
   * 处理删除键
   * @param {Object} input - 输入对象
   * @returns {Promise<boolean>} 是否处理成功
   */
  async handleDelete(input) {
    // 实现删除键逻辑
    this.emitEvent('inputChanged', { input: this.currentInput });
    return true;
  }

  /**
   * 处理Tab键
   * @param {Object} input - 输入对象
   * @returns {Promise<boolean>} 是否处理成功
   */
  async handleTab(input) {
    // 实现自动补全逻辑
    this.emitEvent('autoComplete', { input: this.currentInput });
    return true;
  }

  /**
   * 处理Ctrl+C
   * @param {Object} input - 输入对象
   * @returns {Promise<boolean>} 是否处理成功
   */
  async handleCtrlC(input) {
    this.emitEvent('interrupt', {});
    return true;
  }

  /**
   * 处理Ctrl+D
   * @param {Object} input - 输入对象
   * @returns {Promise<boolean>} 是否处理成功
   */
  async handleCtrlD(input) {
    if (this.currentInput.length === 0) {
      this.emitEvent('exit', {});
    }
    return true;
  }

  /**
   * 处理Ctrl+L
   * @param {Object} input - 输入对象
   * @returns {Promise<boolean>} 是否处理成功
   */
  async handleCtrlL(input) {
    this.emitEvent('clearScreen', {});
    return true;
  }

  /**
   * 处理Ctrl+R
   * @param {Object} input - 输入对象
   * @returns {Promise<boolean>} 是否处理成功
   */
  async handleCtrlR(input) {
    this.emitEvent('toggleMode', {});
    return true;
  }

  /**
   * 处理Ctrl+U
   * @param {Object} input - 输入对象
   * @returns {Promise<boolean>} 是否处理成功
   */
  async handleCtrlU(input) {
    this.currentInput = '';
    this.emitEvent('inputChanged', { input: this.currentInput });
    return true;
  }

  /**
   * 处理Ctrl+K
   * @param {Object} input - 输入对象
   * @returns {Promise<boolean>} 是否处理成功
   */
  async handleCtrlK(input) {
    // 实现从光标位置删除到行尾的逻辑
    this.emitEvent('inputChanged', { input: this.currentInput });
    return true;
  }

  /**
   * 处理Escape键
   * @param {Object} input - 输入对象
   * @returns {Promise<boolean>} 是否处理成功
   */
  async handleEscape(input) {
    this.emitEvent('cancel', {});
    return true;
  }

  /**
   * 处理Cmd+Escape
   * @param {Object} input - 输入对象
   * @returns {Promise<boolean>} 是否处理成功
   */
  async handleCmdEscape(input) {
    this.emitEvent('quickLaunch', {});
    return true;
  }

  /**
   * 处理字符输入
   * @param {string} char - 字符
   * @param {Object} input - 输入对象
   * @returns {boolean} 是否处理成功
   */
  handleCharacterInput(char, input) {
    this.currentInput += char;
    this.emitEvent('inputChanged', { input: this.currentInput });
    return true;
  }

  /**
   * 检查是否为可打印字符
   * @param {string} key - 按键
   * @returns {boolean} 是否为可打印字符
   */
  isPrintableCharacter(key) {
    return key.length === 1 && key >= ' ' && key <= '~';
  }

  /**
   * 添加到历史记录
   * @param {string} input - 输入内容
   */
  addToHistory(input) {
    // 避免重复的历史记录
    if (this.inputHistory.length > 0 && 
        this.inputHistory[this.inputHistory.length - 1] === input) {
      return;
    }

    this.inputHistory.push(input);
    
    // 限制历史记录大小
    if (this.inputHistory.length > this.maxHistorySize) {
      this.inputHistory.shift();
    }
  }

  /**
   * 设置输入模式
   * @param {string} mode - 输入模式
   */
  setInputMode(mode) {
    if (Object.values(INPUT_MODES).includes(mode)) {
      this.inputMode = mode;
      this.emitEvent('inputModeChanged', { mode });
      logger.debug('输入模式已更改', { mode });
    }
  }

  /**
   * 设置焦点状态
   * @param {boolean} hasFocus - 是否有焦点
   */
  setFocus(hasFocus) {
    this.hasFocus = hasFocus;
    this.emitEvent('focusChanged', { hasFocus });
    logger.debug('终端焦点状态已更改', { hasFocus });
  }

  /**
   * 设置原始模式
   * @param {boolean} rawMode - 是否启用原始模式
   */
  setRawMode(rawMode) {
    this.isRawMode = rawMode;
    
    if (process.stdin.setRawMode) {
      process.stdin.setRawMode(rawMode);
    }
    
    logger.debug('终端原始模式已更改', { rawMode });
  }

  /**
   * 注册事件处理器
   * @param {string} event - 事件名称
   * @param {Function} handler - 事件处理器
   */
  on(event, handler) {
    if (!this.eventHandlers.has(event)) {
      this.eventHandlers.set(event, []);
    }
    
    this.eventHandlers.get(event).push(handler);
  }

  /**
   * 移除事件处理器
   * @param {string} event - 事件名称
   * @param {Function} handler - 事件处理器
   */
  off(event, handler) {
    const handlers = this.eventHandlers.get(event);
    
    if (handlers) {
      const index = handlers.indexOf(handler);
      if (index > -1) {
        handlers.splice(index, 1);
      }
    }
  }

  /**
   * 发射事件
   * @param {string} event - 事件名称
   * @param {Object} data - 事件数据
   */
  emitEvent(event, data) {
    const handlers = this.eventHandlers.get(event);
    
    if (handlers) {
      for (const handler of handlers) {
        try {
          handler(data);
        } catch (error) {
          logger.error('事件处理器执行失败', { event, error: error.message });
        }
      }
    }
  }

  /**
   * 获取终端信息
   * @returns {Object} 终端信息
   */
  getTerminalInfo() {
    return {
      status: this.status,
      inputMode: this.inputMode,
      currentInput: this.currentInput,
      historySize: this.inputHistory.length,
      hasFocus: this.hasFocus,
      isRawMode: this.isRawMode,
      isTypingWithoutFocus: this.isTypingWithoutFocus,
      dimensions: {
        columns: process.stdout.columns,
        rows: process.stdout.rows
      }
    };
  }

  /**
   * 清空输入
   */
  clearInput() {
    this.currentInput = '';
    this.historyIndex = -1;
    this.emitEvent('inputChanged', { input: this.currentInput });
  }

  /**
   * 清空历史记录
   */
  clearHistory() {
    this.inputHistory = [];
    this.historyIndex = -1;
    logger.debug('输入历史记录已清空');
  }
}

/**
 * 进程管理器类
 */
export class ProcessManager {
  constructor() {
    this.processes = new Map();
    this.nextProcessId = 1;
  }

  /**
   * 启动进程
   * @param {string} command - 命令
   * @param {Array} args - 参数
   * @param {Object} options - 选项
   * @returns {Promise<Object>} 进程信息
   */
  async startProcess(command, args = [], options = {}) {
    const processId = this.nextProcessId++;
    
    try {
      const result = await executeCommand(command, args, {
        ...options,
        background: true
      });

      const processInfo = {
        id: processId,
        command,
        args,
        process: result.process,
        startTime: Date.now(),
        status: 'running',
        options
      };

      this.processes.set(processId, processInfo);

      // 设置进程事件监听器
      this.setupProcessListeners(processInfo);

      logger.debug('进程已启动', { processId, command });
      
      return processInfo;

    } catch (error) {
      logger.error('进程启动失败', { command, error: error.message });
      throw error;
    }
  }

  /**
   * 设置进程事件监听器
   * @param {Object} processInfo - 进程信息
   */
  setupProcessListeners(processInfo) {
    const { process: proc, id } = processInfo;

    proc.on('exit', (code, signal) => {
      processInfo.status = 'exited';
      processInfo.exitCode = code;
      processInfo.signal = signal;
      processInfo.endTime = Date.now();
      
      logger.debug('进程已退出', { processId: id, code, signal });
    });

    proc.on('error', (error) => {
      processInfo.status = 'error';
      processInfo.error = error.message;
      
      logger.error('进程错误', { processId: id, error: error.message });
    });
  }

  /**
   * 停止进程
   * @param {number} processId - 进程ID
   * @param {string} signal - 信号
   * @returns {boolean} 是否停止成功
   */
  stopProcess(processId, signal = 'SIGTERM') {
    const processInfo = this.processes.get(processId);
    
    if (!processInfo || processInfo.status !== 'running') {
      return false;
    }

    try {
      processInfo.process.kill(signal);
      processInfo.status = 'stopping';
      
      logger.debug('进程停止信号已发送', { processId, signal });
      
      return true;
    } catch (error) {
      logger.error('进程停止失败', { processId, error: error.message });
      return false;
    }
  }

  /**
   * 获取进程信息
   * @param {number} processId - 进程ID
   * @returns {Object|null} 进程信息
   */
  getProcess(processId) {
    return this.processes.get(processId) || null;
  }

  /**
   * 获取所有进程
   * @returns {Array} 进程列表
   */
  getAllProcesses() {
    return Array.from(this.processes.values());
  }

  /**
   * 清理已结束的进程
   */
  cleanupFinishedProcesses() {
    const toRemove = [];
    
    for (const [id, processInfo] of this.processes) {
      if (processInfo.status === 'exited' || processInfo.status === 'error') {
        toRemove.push(id);
      }
    }

    for (const id of toRemove) {
      this.processes.delete(id);
    }

    if (toRemove.length > 0) {
      logger.debug('已清理完成的进程', { count: toRemove.length });
    }
  }

  /**
   * 停止所有进程
   * @returns {Promise<void>}
   */
  async stopAllProcesses() {
    const runningProcesses = Array.from(this.processes.values())
      .filter(p => p.status === 'running');

    for (const processInfo of runningProcesses) {
      this.stopProcess(processInfo.id);
    }

    // 等待进程停止
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // 强制终止仍在运行的进程
    for (const processInfo of runningProcesses) {
      if (processInfo.status === 'running') {
        this.stopProcess(processInfo.id, 'SIGKILL');
      }
    }

    logger.info('所有进程已停止');
  }
}

/**
 * 输入处理器类
 */
export class InputProcessor {
  constructor() {
    this.processors = new Map();
    this.middleware = [];
  }

  /**
   * 注册输入处理器
   * @param {string} mode - 输入模式
   * @param {Function} processor - 处理器函数
   */
  registerProcessor(mode, processor) {
    this.processors.set(mode, processor);
  }

  /**
   * 添加中间件
   * @param {Function} middleware - 中间件函数
   */
  addMiddleware(middleware) {
    this.middleware.push(middleware);
  }

  /**
   * 处理输入
   * @param {string} input - 输入内容
   * @param {string} mode - 输入模式
   * @returns {Promise<Object>} 处理结果
   */
  async processInput(input, mode) {
    let processedInput = input;

    // 应用中间件
    for (const middleware of this.middleware) {
      processedInput = await middleware(processedInput, mode);
    }

    // 获取对应的处理器
    const processor = this.processors.get(mode);
    
    if (!processor) {
      throw new Error(`No processor found for mode: ${mode}`);
    }

    return await processor(processedInput);
  }
}

/**
 * 全局终端管理器实例
 */
export const globalTerminalManager = new TerminalManager();

/**
 * 全局进程管理器实例
 */
export const globalProcessManager = new ProcessManager();

/**
 * 全局输入处理器实例
 */
export const globalInputProcessor = new InputProcessor();

/**
 * 便捷函数：处理键盘输入
 * @param {string} key - 按键
 * @param {Object} input - 输入对象
 * @returns {Promise<boolean>} 是否处理成功
 */
export async function handleKeyInput(key, input) {
  return globalTerminalManager.handleKeyInput(key, input);
}

/**
 * 便捷函数：启动进程
 * @param {string} command - 命令
 * @param {Array} args - 参数
 * @param {Object} options - 选项
 * @returns {Promise<Object>} 进程信息
 */
export async function startProcess(command, args = [], options = {}) {
  return globalProcessManager.startProcess(command, args, options);
}

/**
 * 便捷函数：处理输入
 * @param {string} input - 输入内容
 * @param {string} mode - 输入模式
 * @returns {Promise<Object>} 处理结果
 */
export async function processInput(input, mode = INPUT_MODES.PROMPT) {
  return globalInputProcessor.processInput(input, mode);
}
