/**
 * Claude Code 主应用程序
 * 
 * 这是Claude Code应用程序的核心类，负责初始化所有系统并管理应用程序生命周期
 * 
 * @original: 主应用逻辑 (L57175-57240, L57925-57930)
 */

import { logger } from './utils/logger.js';
import { APP_INFO } from './config/constants.js';
import { logTelemetryEvent } from './services/telemetry.js';

/**
 * 应用程序主类
 */
export class ClaudeCodeApp {
  constructor() {
    this.initialized = false;
    this.shutdownHandlers = [];
    this.services = new Map();
  }

  /**
   * 初始化应用程序
   * @original: 应用初始化逻辑 (L57175-57240)
   * @returns {Promise<void>}
   */
  async initialize() {
    if (this.initialized) {
      return;
    }

    try {
      logger.info('初始化Claude Code应用程序', { version: APP_INFO.VERSION });

      // 验证运行环境
      this.validateEnvironment();

      // 设置进程事件监听器
      this.setupProcessHandlers();

      // 初始化核心系统
      await this.initializeCoreServices();

      // 设置客户端类型
      this.setupClientType();

      this.initialized = true;
      logger.info('Claude Code应用程序初始化完成');

    } catch (error) {
      logger.error('应用程序初始化失败', { error: error.message });
      throw error;
    }
  }

  /**
   * 验证运行环境
   * @original: Node.js版本检查逻辑 (L57176-57177)
   */
  validateEnvironment() {
    // 检查Node.js版本
    const nodeVersion = process.version.match(/^v(\d+)\./)?.[1];
    
    if (!nodeVersion || parseInt(nodeVersion) < 18) {
      console.error('Error: Claude Code requires Node.js version 18 or higher.');
      process.exit(1);
    }

    // 检查CI环境
    if (this.isCIEnvironment()) {
      console.warn('Running in CI environment - interactive features are limited');
    }
  }

  /**
   * 检查是否为CI环境
   * @returns {boolean} 是否为CI环境
   */
  isCIEnvironment() {
    return !!(process.env.CI || process.env.CONTINUOUS_INTEGRATION);
  }

  /**
   * 设置进程事件处理器
   * @original: 进程事件处理逻辑 (L57226-57235)
   */
  setupProcessHandlers() {
    // 退出事件
    process.on('exit', () => {
      this.cleanup();
    });

    // 中断信号
    process.on('SIGINT', () => {
      logger.info('收到中断信号，正在关闭...');
      this.shutdown();
    });

    // 终止信号
    process.on('SIGTERM', () => {
      logger.info('收到终止信号，正在关闭...');
      this.shutdown();
    });

    // 未捕获异常
    process.on('uncaughtException', (error) => {
      logger.error('未捕获异常', { error: error.message, stack: error.stack });
      this.shutdown(1);
    });

    // 未处理的Promise拒绝
    process.on('unhandledRejection', (reason, promise) => {
      logger.error('未处理的Promise拒绝', { reason, promise });
    });
  }

  /**
   * 初始化核心服务
   * @returns {Promise<void>}
   */
  async initializeCoreServices() {
    try {
      // 初始化配置系统
      const { globalConfigManager } = await import('./config/settings.js');
      this.services.set('config', globalConfigManager);
      
      // 初始化遥测系统
      const { globalTelemetryManager, globalOpenTelemetryIntegration } = await import('./services/telemetry.js');
      this.services.set('telemetry', globalTelemetryManager);
      this.services.set('opentelemetry', globalOpenTelemetryIntegration);
      
      if (globalTelemetryManager.enabled) {
        await globalOpenTelemetryIntegration.initialize();
        globalTelemetryManager.startBatchFlush();
      }

      // 初始化会话管理
      const { globalSessionManager } = await import('./core/session.js');
      this.services.set('session', globalSessionManager);
      await globalSessionManager.loadSessions();

      // 初始化工具系统
      const { globalToolManager } = await import('./core/tools.js');
      this.services.set('tools', globalToolManager);
      await globalToolManager.initializeTools();

      // 初始化API客户端
      const { globalAPIClient } = await import('./services/api.js');
      this.services.set('api', globalAPIClient);

      // 初始化MCP管理器
      const { globalMCPManager } = await import('./services/mcp.js');
      this.services.set('mcp', globalMCPManager);

      // 初始化终端管理器
      const { globalTerminalManager } = await import('./services/terminal.js');
      this.services.set('terminal', globalTerminalManager);

      logger.debug('核心服务初始化完成', { serviceCount: this.services.size });

    } catch (error) {
      logger.error('核心服务初始化失败', { error: error.message });
      throw error;
    }
  }

  /**
   * 设置客户端类型
   * @original: oj0()函数调用 (L57241)
   */
  setupClientType() {
    const entrypoint = process.env.CLAUDE_CODE_ENTRYPOINT;
    
    let clientType = 'cli';
    
    if (entrypoint === 'sdk-ts') {
      clientType = 'sdk-typescript';
    } else if (entrypoint === 'sdk-py') {
      clientType = 'sdk-python';
    } else if (entrypoint === 'sdk-cli') {
      clientType = 'sdk-cli';
    }

    // 设置客户端类型（这里需要实现具体的设置逻辑）
    logger.debug('客户端类型已设置', { clientType, entrypoint });
  }

  /**
   * 启动应用程序
   * @param {Array} argv - 命令行参数
   * @returns {Promise<void>}
   */
  async start(argv = process.argv) {
    await this.initialize();

    try {
      // 设置入口点环境变量
      if (!process.env.CLAUDE_CODE_ENTRYPOINT) {
        process.env.CLAUDE_CODE_ENTRYPOINT = 'cli';
      }

      // 解析命令行参数并执行
      const { parseCommandLineArguments } = await import('./cli/commander.js');
      await parseCommandLineArguments(argv);

    } catch (error) {
      logger.error('应用程序启动失败', { error: error.message });
      process.exit(1);
    }
  }

  /**
   * 添加关闭处理器
   * @param {Function} handler - 关闭处理器
   */
  addShutdownHandler(handler) {
    this.shutdownHandlers.push(handler);
  }

  /**
   * 关闭应用程序
   * @param {number} exitCode - 退出代码
   */
  async shutdown(exitCode = 0) {
    logger.info('正在关闭Claude Code应用程序');

    // 执行关闭处理器
    for (const handler of this.shutdownHandlers) {
      try {
        await handler();
      } catch (error) {
        logger.error('关闭处理器执行失败', { error: error.message });
      }
    }

    // 关闭核心服务
    await this.shutdownCoreServices();

    process.exit(exitCode);
  }

  /**
   * 关闭核心服务
   * @returns {Promise<void>}
   */
  async shutdownCoreServices() {
    try {
      // 关闭遥测系统
      const telemetryManager = this.services.get('telemetry');
      const openTelemetryIntegration = this.services.get('opentelemetry');
      
      if (telemetryManager) {
        await telemetryManager.shutdown();
      }
      
      if (openTelemetryIntegration) {
        await openTelemetryIntegration.shutdown();
      }

      // 关闭会话管理
      const sessionManager = this.services.get('session');
      if (sessionManager) {
        sessionManager.stopAutoSave();
      }

      // 关闭MCP服务
      const mcpManager = this.services.get('mcp');
      if (mcpManager) {
        await mcpManager.stopAllServers();
      }

      // 关闭进程管理
      const { globalProcessManager } = await import('./services/terminal.js');
      await globalProcessManager.stopAllProcesses();

      logger.debug('核心服务关闭完成');

    } catch (error) {
      logger.error('核心服务关闭失败', { error: error.message });
    }
  }

  /**
   * 清理资源
   * @original: 清理逻辑 (L57926-57927)
   */
  cleanup() {
    // 恢复终端状态
    const stdout = process.stderr.isTTY ? process.stderr : 
                   process.stdout.isTTY ? process.stdout : undefined;
    
    if (stdout) {
      stdout.write('\x1B[?25h'); // 显示光标
    }
  }

  /**
   * 获取服务实例
   * @param {string} serviceName - 服务名称
   * @returns {Object|null} 服务实例
   */
  getService(serviceName) {
    return this.services.get(serviceName) || null;
  }

  /**
   * 获取应用程序状态
   * @returns {Object} 应用程序状态
   */
  getStatus() {
    return {
      initialized: this.initialized,
      version: APP_INFO.VERSION,
      platform: process.platform,
      nodeVersion: process.version,
      pid: process.pid,
      uptime: process.uptime(),
      memoryUsage: process.memoryUsage(),
      serviceCount: this.services.size,
      services: Array.from(this.services.keys())
    };
  }

  /**
   * 健康检查
   * @returns {Promise<Object>} 健康检查结果
   */
  async healthCheck() {
    const health = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      services: {},
      errors: []
    };

    // 检查各个服务的健康状态
    for (const [serviceName, service] of this.services) {
      try {
        if (service.healthCheck && typeof service.healthCheck === 'function') {
          health.services[serviceName] = await service.healthCheck();
        } else {
          health.services[serviceName] = { status: 'unknown' };
        }
      } catch (error) {
        health.services[serviceName] = { 
          status: 'error', 
          error: error.message 
        };
        health.errors.push(`${serviceName}: ${error.message}`);
      }
    }

    // 如果有错误，标记为不健康
    if (health.errors.length > 0) {
      health.status = 'unhealthy';
    }

    return health;
  }

  /**
   * 重启应用程序
   * @returns {Promise<void>}
   */
  async restart() {
    logger.info('重启Claude Code应用程序');
    
    await this.shutdown(0);
    
    // 重新启动
    setTimeout(() => {
      this.start().catch(error => {
        logger.error('重启失败', { error: error.message });
        process.exit(1);
      });
    }, 1000);
  }
}

/**
 * 全局应用程序实例
 */
export const globalApp = new ClaudeCodeApp();

/**
 * 主函数 - 应用程序入口点
 * @param {Array} argv - 命令行参数
 * @returns {Promise<void>}
 */
export async function main(argv = process.argv) {
  await globalApp.start(argv);
}

/**
 * 便捷函数：获取应用程序实例
 * @returns {ClaudeCodeApp} 应用程序实例
 */
export function getApp() {
  return globalApp;
}

/**
 * 便捷函数：检查应用程序是否已初始化
 * @returns {boolean} 是否已初始化
 */
export function isAppInitialized() {
  return globalApp.initialized;
}

/**
 * 便捷函数：获取应用程序状态
 * @returns {Object} 应用程序状态
 */
export function getAppStatus() {
  return globalApp.getStatus();
}

/**
 * 便捷函数：执行健康检查
 * @returns {Promise<Object>} 健康检查结果
 */
export async function performHealthCheck() {
  return globalApp.healthCheck();
}

/**
 * 便捷函数：重启应用程序
 * @returns {Promise<void>}
 */
export async function restartApp() {
  return globalApp.restart();
}

/**
 * 便捷函数：关闭应用程序
 * @param {number} exitCode - 退出代码
 * @returns {Promise<void>}
 */
export async function shutdownApp(exitCode = 0) {
  return globalApp.shutdown(exitCode);
}
