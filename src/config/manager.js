/**
 * 配置管理器
 * 
 * 负责管理Claude Code的各种配置，包括本地、用户、项目配置等
 * 
 * @original: 配置管理相关代码 (L3009-5130, L57633-57685)
 */

import { readFileSync, writeFileSync, existsSync } from 'fs';
import { join, resolve } from 'path';
import { homedir } from 'os';
import { logger } from '../utils/logger.js';
import { CONFIG_SCOPES } from './constants.js';

/**
 * 配置作用域枚举
 */
export const CONFIG_SCOPE = {
  LOCAL: 'local',
  USER: 'user', 
  PROJECT: 'project',
  POLICY: 'policy',
  FLAG: 'flag',
  DYNAMIC: 'dynamic'
};

/**
 * 配置文件路径
 */
export const CONFIG_PATHS = {
  LOCAL: '.claude/settings.json',
  USER: join(homedir(), '.claude', 'config.json'),
  PROJECT: '.claude/project.json',
  MCP_PROJECT: '.mcp.json'
};

/**
 * 默认配置值
 */
export const DEFAULT_CONFIG = {
  theme: 'auto',
  verbose: false,
  autoUpdates: true,
  installMethod: 'unknown',
  model: undefined,
  permissions: {
    defaultMode: 'ask',
    additionalDirectories: [],
    disableBypassPermissionsMode: false
  },
  mcpServers: {},
  allowedTools: [],
  ignorePatterns: [],
  env: {}
};

/**
 * 项目配置默认值
 */
export const DEFAULT_PROJECT_CONFIG = {
  mcpServers: {},
  allowedTools: [],
  ignorePatterns: [],
  enabledMcpjsonServers: [],
  disabledMcpjsonServers: [],
  enableAllProjectMcpServers: false,
  hooks: {},
  statusLine: undefined
};

/**
 * 配置管理器类
 */
export class ConfigManager {
  constructor() {
    this.cache = new Map();
    this.watchers = new Map();
  }

  /**
   * 获取配置文件路径
   * @param {string} scope - 配置作用域
   * @returns {string} 配置文件路径
   */
  getConfigPath(scope) {
    switch (scope) {
      case CONFIG_SCOPE.LOCAL:
        return resolve(process.cwd(), CONFIG_PATHS.LOCAL);
      case CONFIG_SCOPE.USER:
        return CONFIG_PATHS.USER;
      case CONFIG_SCOPE.PROJECT:
        return resolve(process.cwd(), CONFIG_PATHS.PROJECT);
      default:
        throw new Error(`Unknown config scope: ${scope}`);
    }
  }

  /**
   * 读取配置文件
   * @original: f8()函数的读取逻辑 (L5102)
   * @param {string} scope - 配置作用域
   * @returns {Object} 配置对象
   */
  readConfig(scope) {
    if (scope === CONFIG_SCOPE.POLICY || scope === CONFIG_SCOPE.FLAG) {
      return { error: null };
    }

    const configPath = this.getConfigPath(scope);
    
    // 检查缓存
    const cacheKey = `${scope}:${configPath}`;
    if (this.cache.has(cacheKey)) {
      const cached = this.cache.get(cacheKey);
      // 简单的缓存失效检查（实际应该检查文件修改时间）
      if (Date.now() - cached.timestamp < 5000) {
        return cached.config;
      }
    }

    try {
      if (!existsSync(configPath)) {
        const defaultConfig = scope === CONFIG_SCOPE.PROJECT 
          ? DEFAULT_PROJECT_CONFIG 
          : DEFAULT_CONFIG;
        
        this.cache.set(cacheKey, {
          config: defaultConfig,
          timestamp: Date.now()
        });
        
        return defaultConfig;
      }

      const content = readFileSync(configPath, 'utf8');
      const config = JSON.parse(content);
      
      // 合并默认配置
      const defaultConfig = scope === CONFIG_SCOPE.PROJECT 
        ? DEFAULT_PROJECT_CONFIG 
        : DEFAULT_CONFIG;
      const mergedConfig = { ...defaultConfig, ...config };

      this.cache.set(cacheKey, {
        config: mergedConfig,
        timestamp: Date.now()
      });

      logger.debug('配置文件读取成功', { scope, configPath });
      return mergedConfig;

    } catch (error) {
      logger.error('配置文件读取失败', { scope, configPath, error: error.message });
      
      if (error instanceof SyntaxError) {
        return {
          error: new Error(`Invalid JSON syntax in settings file at ${configPath}`)
        };
      }
      
      return {
        error: new Error(`Failed to read raw settings from ${configPath}: ${error.message}`)
      };
    }
  }

  /**
   * 写入配置文件
   * @original: f8()函数的写入逻辑 (L5122)
   * @param {string} scope - 配置作用域
   * @param {Object} config - 配置对象
   * @returns {Object} 操作结果
   */
  writeConfig(scope, config) {
    if (scope === CONFIG_SCOPE.POLICY || scope === CONFIG_SCOPE.FLAG) {
      return { error: null };
    }

    const configPath = this.getConfigPath(scope);

    try {
      // 读取现有配置
      const existingConfig = this.readConfig(scope);
      if (existingConfig.error) {
        return existingConfig;
      }

      // 合并配置
      const mergedConfig = this.mergeConfig(existingConfig, config);

      // 写入文件
      const configJson = JSON.stringify(mergedConfig, null, 2);
      writeFileSync(configPath, configJson, 'utf8');

      // 更新缓存
      const cacheKey = `${scope}:${configPath}`;
      this.cache.set(cacheKey, {
        config: mergedConfig,
        timestamp: Date.now()
      });

      logger.debug('配置文件写入成功', { scope, configPath });

      // 特殊处理：本地设置需要添加到git ignore
      if (scope === CONFIG_SCOPE.LOCAL) {
        this.addToGitIgnore(CONFIG_PATHS.LOCAL);
      }

      return { error: null };

    } catch (error) {
      logger.error('配置文件写入失败', { scope, configPath, error: error.message });
      return {
        error: new Error(`Failed to write settings to ${configPath}: ${error.message}`)
      };
    }
  }

  /**
   * 合并配置对象
   * @param {Object} existing - 现有配置
   * @param {Object} updates - 更新配置
   * @returns {Object} 合并后的配置
   */
  mergeConfig(existing, updates) {
    const merged = { ...existing };

    for (const [key, value] of Object.entries(updates)) {
      if (value === undefined) {
        // 删除键
        delete merged[key];
      } else if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
        // 深度合并对象
        merged[key] = this.mergeConfig(merged[key] || {}, value);
      } else {
        // 直接赋值
        merged[key] = value;
      }
    }

    return merged;
  }

  /**
   * 获取配置值
   * @original: k$2()函数 (L6485)
   * @param {string} key - 配置键
   * @param {boolean} global - 是否为全局配置
   * @returns {*} 配置值
   */
  getConfigValue(key, global = false) {
    const scope = global ? CONFIG_SCOPE.USER : CONFIG_SCOPE.PROJECT;
    const config = this.readConfig(scope);
    
    if (config.error) {
      throw config.error;
    }

    return config[key];
  }

  /**
   * 设置配置值
   * @original: _$2()函数 (L6494)
   * @param {string} key - 配置键
   * @param {*} value - 配置值
   * @param {boolean} global - 是否为全局配置
   */
  setConfigValue(key, value, global = false) {
    const scope = global ? CONFIG_SCOPE.USER : CONFIG_SCOPE.PROJECT;
    
    // 验证配置键
    if (!this.isValidConfigKey(key, global)) {
      const validKeys = global ? this.getValidGlobalKeys() : this.getValidProjectKeys();
      throw new Error(`Invalid config key: ${key}. Valid keys are: ${validKeys.join(', ')}`);
    }

    // 处理数组类型的自动转换
    if (this.isArrayConfigKey(key, global) && typeof value === 'string') {
      logger.warn(`Warning: '${key}' is an array type. Automatically using 'config add' instead of 'config set'.`);
      const values = value.split(',').map(v => v.trim()).filter(v => v.length > 0);
      return this.addToConfigArray(key, values, global);
    }

    const updates = { [key]: value };
    const result = this.writeConfig(scope, updates);
    
    if (result.error) {
      throw result.error;
    }

    logger.info(`Set ${key} to ${value}`);
  }

  /**
   * 删除配置值
   * @original: x$2()函数
   * @param {string} key - 配置键
   * @param {boolean} global - 是否为全局配置
   */
  deleteConfigValue(key, global = false) {
    const scope = global ? CONFIG_SCOPE.USER : CONFIG_SCOPE.PROJECT;
    const updates = { [key]: undefined };
    
    const result = this.writeConfig(scope, updates);
    if (result.error) {
      throw result.error;
    }

    logger.info(`Removed ${key}`);
  }

  /**
   * 添加到配置数组
   * @original: aM1()函数 (L6367)
   * @param {string} key - 配置键
   * @param {string[]} values - 要添加的值
   * @param {boolean} global - 是否为全局配置
   */
  addToConfigArray(key, values, global = false) {
    if (!this.isArrayConfigKey(key, global)) {
      throw new Error(`'${key}' is not a valid array config key`);
    }

    const scope = global ? CONFIG_SCOPE.USER : CONFIG_SCOPE.PROJECT;
    const config = this.readConfig(scope);
    
    if (config.error) {
      throw config.error;
    }

    const currentArray = config[key] || [];
    const newArray = [...new Set([...currentArray, ...values])]; // 去重

    const updates = { [key]: newArray };
    const result = this.writeConfig(scope, updates);
    
    if (result.error) {
      throw result.error;
    }

    logger.info(`Added to ${key}: ${values.join(', ')}`);
  }

  /**
   * 从配置数组中移除
   * @original: T$2()函数
   * @param {string} key - 配置键
   * @param {string[]} values - 要移除的值
   * @param {boolean} global - 是否为全局配置
   */
  removeFromConfigArray(key, values, global = false) {
    if (!this.isArrayConfigKey(key, global)) {
      throw new Error(`'${key}' is not a valid array config key`);
    }

    const scope = global ? CONFIG_SCOPE.USER : CONFIG_SCOPE.PROJECT;
    const config = this.readConfig(scope);
    
    if (config.error) {
      throw config.error;
    }

    const currentArray = config[key] || [];
    const newArray = currentArray.filter(item => !values.includes(item));

    const updates = { [key]: newArray };
    const result = this.writeConfig(scope, updates);
    
    if (result.error) {
      throw result.error;
    }

    logger.info(`Removed from ${key}: ${values.join(', ')}`);
  }

  /**
   * 列出所有配置
   * @original: v$2()函数
   * @param {boolean} global - 是否为全局配置
   * @returns {Object} 所有配置
   */
  listAllConfig(global = false) {
    const scope = global ? CONFIG_SCOPE.USER : CONFIG_SCOPE.PROJECT;
    const config = this.readConfig(scope);
    
    if (config.error) {
      throw config.error;
    }

    return config;
  }

  /**
   * 清空缓存
   */
  clearCache() {
    this.cache.clear();
    logger.debug('配置缓存已清空');
  }

  /**
   * 添加到git ignore
   * @param {string} pattern - 忽略模式
   */
  addToGitIgnore(pattern) {
    // 实现git ignore添加逻辑
    logger.debug('添加到git ignore', { pattern });
  }

  // 辅助方法
  isValidConfigKey(key, global) {
    const validKeys = global ? this.getValidGlobalKeys() : this.getValidProjectKeys();
    return validKeys.includes(key);
  }

  isArrayConfigKey(key, global) {
    const arrayKeys = global 
      ? ['allowedTools', 'ignorePatterns']
      : ['allowedTools', 'ignorePatterns', 'enabledMcpjsonServers', 'disabledMcpjsonServers'];
    return arrayKeys.includes(key);
  }

  getValidGlobalKeys() {
    return Object.keys(DEFAULT_CONFIG);
  }

  getValidProjectKeys() {
    return Object.keys(DEFAULT_PROJECT_CONFIG);
  }
}

/**
 * 全局配置管理器实例
 */
export const globalConfigManager = new ConfigManager();

/**
 * 便捷函数：读取本地设置
 * @original: $Y()函数
 * @param {string} scope - 配置作用域
 * @returns {Object} 配置对象
 */
export function getSettings(scope = CONFIG_SCOPE.LOCAL) {
  return globalConfigManager.readConfig(scope);
}

/**
 * 便捷函数：写入设置
 * @original: f8()函数
 * @param {string} scope - 配置作用域
 * @param {Object} updates - 更新配置
 * @returns {Object} 操作结果
 */
export function updateSettings(scope, updates) {
  return globalConfigManager.writeConfig(scope, updates);
}
