/**
 * 应用程序常量定义
 * 
 * 包含Claude Code应用程序中使用的各种常量
 */

/**
 * 应用程序版本和信息
 * @original: 版本信息散布在代码中
 */
export const APP_INFO = {
  NAME: 'Claude Code',
  VERSION: '1.0.72',
  PACKAGE_URL: '@anthropic-ai/claude-code',
  README_URL: 'https://docs.anthropic.com/s/claude-code',
  ISSUES_URL: 'https://github.com/anthropics/claude-code/issues'
};

/**
 * 权限模式
 * @original: _K1数组和相关权限模式定义
 */
export const PERMISSION_MODES = [
  'ask',
  'allow',
  'deny',
  'bypass'
];

/**
 * 文件读取相关常量
 * @original: H91, t6Q等常量 (L2951-2952)
 */
export const FILE_READING = {
  DEFAULT_MAX_LINES: 2000,
  MAX_LINE_LENGTH: 2000,
  MAX_PDF_SIZE: 33554432, // 32MB
  SUPPORTED_PDF_EXTENSIONS: ['pdf']
};

/**
 * 搜索相关常量
 */
export const SEARCH = {
  MAX_BUFFER_SIZE: 20000000, // 20MB
  DEFAULT_TIMEOUT: 10000, // 10秒
  MAX_RESULTS_DEFAULT: 1000
};

/**
 * 缓存相关常量
 */
export const CACHE = {
  DEFAULT_MAX_SIZE: 1000,
  FILE_CACHE_MAX_SIZE: 1000
};

/**
 * API和模型相关常量
 */
export const API = {
  DEFAULT_REGION: 'us-east5',
  CONTEXT_MANAGEMENT_VERSION: 'context-management-2025-06-27'
};

/**
 * 支持的模型列表
 */
export const MODELS = {
  CLAUDE_3_5_HAIKU: 'claude-3-5-haiku',
  CLAUDE_3_5_SONNET: 'claude-3-5-sonnet',
  CLAUDE_3_7_SONNET: 'claude-3-7-sonnet',
  CLAUDE_OPUS_4_1: 'claude-opus-4-1',
  CLAUDE_OPUS_4: 'claude-opus-4',
  CLAUDE_SONNET_4: 'claude-sonnet-4'
};

/**
 * 模型别名映射
 */
export const MODEL_ALIASES = {
  'sonnet': MODELS.CLAUDE_3_5_SONNET,
  'haiku': MODELS.CLAUDE_3_5_HAIKU,
  'opus': MODELS.CLAUDE_OPUS_4
};

/**
 * 输出格式选项
 */
export const OUTPUT_FORMATS = [
  'text',
  'json',
  'stream-json'
];

/**
 * 输入格式选项
 */
export const INPUT_FORMATS = [
  'text',
  'stream-json'
];

/**
 * 支持的传输类型（MCP）
 */
export const TRANSPORT_TYPES = [
  'stdio',
  'sse',
  'http'
];

/**
 * 配置作用域
 */
export const CONFIG_SCOPES = [
  'local',
  'user',
  'project'
];

/**
 * 工具名称常量
 */
export const TOOLS = {
  READ: 'Read',
  BASH: 'Bash',
  EDIT: 'Edit',
  WEB_FETCH: 'WebFetch'
};

/**
 * 错误类型
 */
export const ERROR_TYPES = {
  SHELL_ERROR: 'ShellError',
  ABORT_ERROR: 'AbortError',
  TELEPORT_OPERATION_ERROR: 'TeleportOperationError',
  RUNTIME_ERROR: 'RuntimeError'
};

/**
 * 事件类型
 */
export const EVENT_TYPES = {
  SESSION_START: 'tengu_init',
  STARTUP_TELEMETRY: 'tengu_startup_telemetry',
  DOCTOR_COMMAND: 'tengu_doctor_command',
  MCP_START: 'tengu_mcp_start',
  INSTALL_COMMAND: 'tengu_claude_install_command',
  MIGRATION_START: 'tengu_forced_migration_start',
  MIGRATION_SUCCESS: 'tengu_forced_migration_success',
  MIGRATION_FAILURE: 'tengu_forced_migration_failure'
};

/**
 * 默认配置值
 */
export const DEFAULTS = {
  VERBOSE: false,
  DEBUG: false,
  MAX_TURNS: undefined,
  MAX_THINKING_TOKENS: 0,
  PERMISSION_MODE: 'ask',
  OUTPUT_FORMAT: 'text',
  INPUT_FORMAT: 'text',
  MCP_TRANSPORT: 'stdio',
  CONFIG_SCOPE: 'local'
};

/**
 * 环境变量名称
 */
export const ENV_VARS = {
  NODE_ENV: 'NODE_ENV',
  CLAUDE_LOG_LEVEL: 'CLAUDE_LOG_LEVEL',
  CLOUD_ML_REGION: 'CLOUD_ML_REGION',
  CLAUDE_BASH_MAINTAIN_PROJECT_WORKING_DIR: 'CLAUDE_BASH_MAINTAIN_PROJECT_WORKING_DIR',
  USE_BUILTIN_RIPGREP: 'USE_BUILTIN_RIPGREP',
  ANTHROPIC_MODEL: 'ANTHROPIC_MODEL',
  VERTEX_REGION_CLAUDE_3_5_HAIKU: 'VERTEX_REGION_CLAUDE_3_5_HAIKU',
  VERTEX_REGION_CLAUDE_3_5_SONNET: 'VERTEX_REGION_CLAUDE_3_5_SONNET',
  VERTEX_REGION_CLAUDE_3_7_SONNET: 'VERTEX_REGION_CLAUDE_3_7_SONNET',
  VERTEX_REGION_CLAUDE_4_1_OPUS: 'VERTEX_REGION_CLAUDE_4_1_OPUS',
  VERTEX_REGION_CLAUDE_4_0_OPUS: 'VERTEX_REGION_CLAUDE_4_0_OPUS',
  VERTEX_REGION_CLAUDE_4_0_SONNET: 'VERTEX_REGION_CLAUDE_4_0_SONNET'
};

/**
 * 文件扩展名和类型映射
 */
export const FILE_TYPES = {
  IMAGES: ['.png', '.jpg', '.jpeg', '.gif', '.bmp', '.webp', '.svg'],
  DOCUMENTS: ['.pdf', '.doc', '.docx', '.txt', '.md', '.rtf'],
  CODE: ['.js', '.ts', '.py', '.java', '.cpp', '.c', '.h', '.css', '.html', '.json', '.xml', '.yaml', '.yml'],
  NOTEBOOKS: ['.ipynb']
};

/**
 * 默认忽略模式（用于文件搜索）
 */
export const DEFAULT_IGNORE_PATTERNS = [
  'node_modules',
  '.git',
  '.svn',
  '.hg',
  'dist',
  'build',
  'target',
  '*.log',
  '*.tmp',
  '.DS_Store',
  'Thumbs.db'
];

/**
 * 终端相关常量
 */
export const TERMINAL = {
  DEFAULT_WIDTH: 80,
  DEFAULT_HEIGHT: 24,
  ESCAPE_SEQUENCES: {
    CLEAR_SCREEN: '\x1B[2J',
    CURSOR_HOME: '\x1B[H',
    SHOW_CURSOR: '\x1B[?25h',
    HIDE_CURSOR: '\x1B[?25l'
  }
};

/**
 * 网络相关常量
 */
export const NETWORK = {
  DEFAULT_TIMEOUT: 30000, // 30秒
  MAX_RETRIES: 3,
  RETRY_DELAY: 1000 // 1秒
};

/**
 * 验证常量值的有效性
 */
export function validateConstants() {
  // 验证权限模式
  if (!Array.isArray(PERMISSION_MODES) || PERMISSION_MODES.length === 0) {
    throw new Error('PERMISSION_MODES must be a non-empty array');
  }

  // 验证输出格式
  if (!Array.isArray(OUTPUT_FORMATS) || OUTPUT_FORMATS.length === 0) {
    throw new Error('OUTPUT_FORMATS must be a non-empty array');
  }

  // 验证应用信息
  if (!APP_INFO.NAME || !APP_INFO.VERSION) {
    throw new Error('APP_INFO must contain NAME and VERSION');
  }

  return true;
}

// 在模块加载时验证常量
validateConstants();
